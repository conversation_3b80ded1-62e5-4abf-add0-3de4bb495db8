<script lang="ts" setup>
import { WinnerAgreementEN } from '@components';
import { useAsync, useTrackData } from '@composables';
import { SILVER_COIN } from '@repositories';
import { downloadPDF } from '@helpers';
import type { ISilverCoin } from '@types';

interface Props {
  coin: ISilverCoin;
  fromMap?: boolean;
}

interface Emits {
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

// const storeUser = useUserStore();
// const { dataVerification } = storeToRefs(storeUser);
const { openDialog } = useMicroRoute();
const { t } = useI18n();
// const { closeMessage, triggerMessage } = useBottomSequences(false);
const { track } = useTrackData();

const { loading, execute: handleViewDetailSubmit } = useAsync({
  async fn() {
    if (!props.fromMap)
      return openDialog('details_winner', {
        winner_info: props.coin.winner_info,
      });
    else {
      const { data } = await SILVER_COIN.getSubmmitedDetails(props.coin._id);
      openDialog('details_winner', {
        winner_info: data,
      });
    }
  },
});

function onClose() {
  emits('close');
  track('silvercoin_congrats', {
    action: 'silvercoin_congrats_close',
  });
  // if (dataVerification.value?.fromMap) {
  // closeMessage();
  // triggerMessage('dialogue_signup_guestreminder_5');
  // }
}
</script>

<template>
  <Dialog @close="onClose">
    <template #header>
      <div
        v-html="
          t('SILVERCOINPOPUP_COIN_1', {
            ORDER: coin.coin_number,
            BRAND_NAME: `${coin.prefix} ${coin.brand_name}` || 'Silver',
          })
        "
      ></div>
    </template>

    <div style="display: none">
      <div id="pdf-content">
        <WinnerAgreementEN :reward="coin.reward" />
        <div class="my-5"></div>
        <br />
        <br />
        <br />
        <br />
        <!-- <WinnerAgreementVN /> -->
      </div>
    </div>
    <div class="text-center px-2">
      <div
        class="text-lg font-bold mb-5"
        v-html="t('SILVERCOIN_SILVERCOINFOUND_SUBMITTED_1')"
      ></div>
      <div class="silver-coin">
        <Icon
          class="absolute top-[55%] -translate-y-1/2"
          name="silver-coin"
          :size="130"
        />
      </div>
      <div
        class="text-sm mb-5 -mt-20"
        v-html="t('SILVERCOIN_SILVERCOINFOUND_SUBMITTED_2')"
      ></div>
      <div
        class="text-sm mb-5"
        v-html="t('SILVERCOIN_SILVERCOINFOUND_SUBMITTED_3')"
      ></div>

      <div class="mx-auto">
        <Button
          :label="t('SILVERCOIN_SILVERCOINFOUND_VIEWDETAILSBUTTON')"
          class="mb-5"
          block
          :loading="loading"
          @click="
            track('silvercoin_congrats', {
              action: 'silvercoin_congrats_viewdetails',
            });
            handleViewDetailSubmit();
          "
        />

        <Button
          :label="t('SILVERCOIN_SILVERCOINFOUND_WINNERSBUTTON')"
          block
          variant="purple"
          @click="
            track('silvercoin_congrats', {
              action: 'silvercoin_congrats_viewagreement',
            });
            downloadPDF('pdf-content');
          "
        />
      </div>
    </div>
  </Dialog>
</template>

<style lang="scss" scoped>
.silver-coin {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 60px);
  aspect-ratio: 1 / 1;
  background-image: url('/imgs/congrats-bg.png');
  background-size: cover;
  margin-left: -30px;
  margin-top: -100px;
}
</style>
