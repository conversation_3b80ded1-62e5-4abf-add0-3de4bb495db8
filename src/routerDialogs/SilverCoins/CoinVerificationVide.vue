<script setup lang="ts">
import { useTick, useTrackData } from '@composables';
import { getSocials } from '@helpers';
import type { ISilverCoin } from '@types';

interface Props {
  coin: ISilverCoin;
}

interface Emits {
  (event: 'close'): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const { t } = useI18n();
const { now } = useTick();
const { track } = useTrackData();

const submitedAt = computed(() =>
  props.coin?.verification_started_at
    ? +new Date(props.coin?.verification_started_at)
    : +new Date()
);

const countdown = computed(() => {
  const days = 2 * 24 * 3600 * 1000; // 2 days (48 hours)
  const cd = submitedAt.value + days;
  const remaining = cd - now.value;

  if (remaining <= 0) {
    return '00h 00m 00s';
  }

  const hours = Math.floor(remaining / (1000 * 60 * 60));
  const minutes = Math.floor((remaining % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((remaining % (1000 * 60)) / 1000);
  return `${hours}h ${minutes}m ${seconds}s`;
});
</script>
<template>
  <Dialog
    @close="
      track('silvercoin_coinfound', {
        action: 'silvercoin_coinfound_close',
      });
      emits('close');
    "
  >
    <template #header>
      <div
        v-html="
          coin.type === 'golden'
            ? t('GOLDENCOIN_FOUND_CONGRATS')
            : t('SILVERCOIN_SILVERCOINFOUND_TITLE_VERIFYING', {
                ORDER: coin.coin_number,
                BRAND_NAME: `${coin.prefix} ${coin.brand_name}` || 'Silver',
              })
        "
      ></div>
    </template>

    <div class="px-2 text-center">
      <div
        class="mb-10 text-lg font-bold"
        v-html="t('SILVERCOIN_SILVERCOINFOUND_1')"
      ></div>
      <div class="silver-coin">
        <Icon
          class="mt-10"
          :name="coin.type === 'golden' ? 'golden-coin' : 'silver-coin'"
          :size="150"
        />
      </div>

      <div
        class="mb-1 -mt-10 text-sm"
        v-html="t('SILVERCOIN_SILVERCOINFOUND_2')"
      ></div>

      <div class="mb-5 text-2xl font-bold">
        {{ countdown }}
      </div>

      <div class="text-center">
        <div
          class="mb-5 text-sm"
          v-html="t('SILVERCOIN_SILVERCOINFOUND_CTA')"
        ></div>
        <div class="flex items-center justify-center gap-4">
          <a
            :href="link"
            target="_blank"
            rel="noopener noreferrer"
            v-for="{ link, icon } in getSocials()"
            :key="icon"
            @click="
              track('silvercoin_coinfound', {
                action: 'silvercoin_coinfound_social',
                link,
                type: icon,
              })
            "
          >
            <Icon :name="icon" :size="25" />
          </a>
        </div>
      </div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.silver-coin {
  display: flex;
  justify-content: center;
  align-items: center;
  width: calc(100% + 60px);
  aspect-ratio: 1/1;
  background-image: url('/imgs/congrats-bg.png');
  background-size: cover;
  margin-left: -30px;
  margin-top: -100px;
}
</style>
