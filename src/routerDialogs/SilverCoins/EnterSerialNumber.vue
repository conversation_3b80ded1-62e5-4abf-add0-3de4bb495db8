<script lang="ts" setup>
import { useForm } from 'vee-validate';
import { useAsync, useClick, useTick, useTrackData } from '@composables';
import { SILVER_COIN } from '@repositories';
import { timeCountDown } from '@helpers';
import { useUserStore, useDialogStore } from '@stores';
import type { IAPIResponseError } from '@types';
import * as yup from 'yup';

type IStage = 'enter_serial_number' | 'continue';

interface Props {
  stage?: IStage;
  fromMap?: boolean;
}

interface Emits {
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const storeUser = useUserStore();
const storeDialog = useDialogStore();

const { user, dataVerification } = storeToRefs(storeUser);
const { t } = useI18n();
const { now } = useTick();
const { push, openDialog } = useMicroRoute();
const { track } = useTrackData();

const stage = ref<IStage>(props.stage || 'enter_serial_number');
const attempts = ref(user.value?.verify_coin_attempts || 0);
const lock_until = ref('');

useClick('ENSURE_FAIRNESS', () => {
  onclose();
  if (stage.value === 'enter_serial_number')
    track('silvercoin_found', {
      action: 'silvercoin_found_confirmation_ensuringfairness',
    });
  else
    track('silvercoin_found_confirmation', {
      action: 'silvercoin_found_confirmation_ensuringfairness',
    });
  push('ensuring_fairness_v2');
});

const validationSchema = yup.object({
  serial_number: yup.string().required(t('SERIAL_NUMBER_REQUIRED')),
});

const { handleSubmit, setFieldError, values } = useForm({
  initialValues: {
    serial_number: '',
  },
  validationSchema,
});

function msgError(lock_until: string) {
  return t('SERIALNUMBER_ERROR_1', {
    ATTEMPTS: attempts.value,
    TIME: timeCountDown(+new Date(lock_until) - now.value),
  });
}

const callback = handleSubmit(async (values) => {
  const { data } = await SILVER_COIN.verifySerialNumber(values);
  await storeUser.fetchUser();
  return data;
});

const { loading, execute: onSubmit } = useAsync({
  async fn() {
    const data = await callback();
    return data;
  },
  onSuccess(data) {
    if (!data) return;
    storeUser.setDataVerification(data);

    stage.value = 'continue';

    track('found_coin_series', {
      result: true,
    });
    track('silvercoin_found', {
      action: 'silvercoin_found_confirmation_continue',
      result: true,
    });

    // TO DO: set value to trigger bottom sequences dialogs
    storeDialog.triggerGuestReminder = 'enter_serial_number';
  },
  onError(err: IAPIResponseError) {
    if (err.error_message === 'eligible') {
      openDialog('coin_limit');
      return;
    }
    attempts.value++;
    const { data, error_message } = err;
    if (data?.lock_until) {
      lock_until.value = data.lock_until;
      setFieldError('serial_number', msgError(data.lock_until));
    } else if (error_message === 'coin_forfeited')
      setFieldError('serial_number', t('POPUP_COIN_FORFEITED'));
    else setFieldError('serial_number', error_message);
    track('found_coin_series', {
      result: false,
    });
    track('silvercoin_found', {
      action: 'silvercoin_found_confirmation_continue',
      result: false,
    });
  },
});

function handleContinue() {
  track('silvercoin_found_confirmation', {
    action: 'silvercoin_found_confirmation_continue',
  });
  if (props.stage === 'continue') {
    onclose();
    let step = 1;
    if (!dataVerification.value?.winner_info?.mobile_number) step = 1;
    else if (
      !dataVerification.value?.winner_info?.reached_minimum_age &&
      !dataVerification.value?.winner_info?.parent_first_name
    )
      step = 2;
    else if (!dataVerification.value?.winner_info?.accepted_tac) step = 3;
    else if (!dataVerification.value?.winner_info?.bank_account_name) step = 4;
    // else  (!dataVerification.value?.winner_info?.sent_video) step = 5
    else step = 5;
    openDialog('enter_verify_form', {
      step,
      serial_number: values.serial_number,
    });
    return;
  }

  push('found_silver_coin_congrat', {
    serial_number: values.serial_number,
  });
  onclose();
}

watch(now, (_now) => {
  if (!values.serial_number) return;
  if (+new Date(lock_until.value) > _now) {
    const el = document.querySelector('.error_msg');
    if (el) el.innerHTML = msgError(lock_until.value);
  } else {
    lock_until.value = '';
    setFieldError('serial_number', '');
  }
});

function onclose() {
  storeDialog.enterSerialNumberTicked = true;

  emits('close');
}

function getCoinName() {
  const coinNumber =
    dataVerification.value?.coin_number ||
    dataVerification.value?.coin?.coin_number;

  const brand_name =
    dataVerification.value?.brand_name ||
    dataVerification.value?.coin?.brand_name;

  const prefix =
    dataVerification.value?.prefix || dataVerification.value?.coin?.prefix;

  return t('SILVERCOINPOPUP_COIN_1', {
    ORDER: coinNumber,
    BRAND_NAME: `${prefix} ${brand_name}` || 'Silver',
  });
}

onMounted(() => {
  if (props.fromMap && dataVerification.value)
    dataVerification.value.fromMap = true;
});
</script>

<template>
  <Dialog
    @close="
      track('silvercoin_found', {
        action: 'silvercoin_found_close',
      });
      onclose();
    "
    :hide-close="stage === 'continue'"
  >
    <template #header>
      <div v-html="t('SERIALNUMBER_TITLE')"></div>
    </template>
    <q-form @submit="onSubmit" class="text-center">
      <section v-show="stage === 'enter_serial_number'">
        <div class="mb-5 text-sm" v-html="t('SERIALNUMBER_DESC')"></div>
        <VeeInput
          class="mb-5"
          name="serial_number"
          :label="t('ENTERSERIALNUMBER_LABEL')"
          autofocus
        />
        <div class="mb-5" v-html="t('SERIALNUMBER_ENSURINGFAIRNESS')"></div>
        <Button
          type="submit"
          :disable="!!lock_until"
          :label="
            !lock_until
              ? t('SERIALNUMBER_BUTTON_SUBMIT')
              : t('SERIALNUMBER_BUTTON_SUBMIT_LOCKED', {
                  TIME: timeCountDown(+new Date(lock_until) - now),
                })
          "
          :loading="loading"
        />
      </section>
      <section v-show="stage === 'continue'" v-if="dataVerification">
        <div class="mb-5 text-sm" v-html="t('SERIALNUMBER_CONTINUE')"></div>
        <div class="mb-5 text-lg font-bold" v-html="getCoinName()"></div>
        <div class="mb-5" v-html="t('SERIALNUMBER_ENSURINGFAIRNESS')"></div>
        <Button
          :label="t('SERIALNUMBER_BUTTON_CONTINUE')"
          @click="handleContinue"
        />
      </section>
    </q-form>
  </Dialog>
</template>
