<script lang="ts" setup>
import {
  delay,
  useCoinSonar,
  useInventory,
  useMetalDetector,
} from '@composables';
import { InventoryItemType } from '@constants';
import { useDialogStore } from '@stores';
import { InventoryItem } from '@types';
import { last } from 'lodash';

interface Props {
  inventoryItem: InventoryItem;
}

const props = defineProps<Props>();

const storeDialog = useDialogStore();

const { t } = useI18n();
const { closeDialog, openDialog, push, currentPath } = useMicroRoute();
const { itemDisplayNames, itemAssetNames, getItemExpirationText, totalItems } =
  useInventory();
const { handleRequestCoinSonar } = useCoinSonar();
const { metalDetectorAction } = useMetalDetector();

const isHome = computed(() => last(currentPath.value.split('/')) === 'home');

const disabled = computed(() => {
  if (
    props.inventoryItem.item_type === InventoryItemType.BEACON &&
    totalItems.value.totalBeacon <= 0
  )
    return true;
  return false;
});

// function openDiscard() {
//   closeDialog('inventory_detail');
//   openDialog('inventory_discard', {
//     inventoryItem: props.inventoryItem,
//   });
// }

function openHowToUse() {
  closeDialog('inventory_detail');
  openDialog('inventory_how_to_use', {
    inventoryItem: props.inventoryItem,
  });
}

async function handleUse() {
  const silverShrink = () => {
    storeDialog.inventoryItem = props.inventoryItem;
    storeDialog.showSilverCoinSelectCircle = true;
  };

  const metalDetector = () => {
    storeDialog.inventoryItem = props.inventoryItem;
    metalDetectorAction();
  };

  const handler = {
    [InventoryItemType.SHRINK]: silverShrink,
    [InventoryItemType.SHRINK_LITE]: silverShrink,
    [InventoryItemType.COIN_SONAR]: () => handleRequestCoinSonar(true),
    [InventoryItemType.METAL_DETECTOR]: metalDetector,
    [InventoryItemType.BEACON]: () => (storeDialog.showBeaconGUI = true),
  };
  const fn = handler[props.inventoryItem.item_type];
  closeDialog('inventory_detail');
  if (!isHome.value) push(-1);
  await delay(500);
  fn();
}
</script>
<template>
  <Dialog>
    <template #header>
      <div class="text-center">
        <div
          class="text-lg font-bold"
          v-html="
            inventoryItem.item_type === InventoryItemType.COIN_SONAR
              ? `${itemDisplayNames[inventoryItem.item_type]}
              (${inventoryItem.radius}m)`
              : itemDisplayNames[inventoryItem.item_type]
          "
        ></div>
        <div
          class="text-[#FF7878] mt-1 text-sm"
          v-html="getItemExpirationText(inventoryItem)"
        ></div>
      </div>
    </template>
    <div class="text-center px-5 relative">
      <!-- <Button
        shape="square"
        variant="secondary"
        class="absolute top-0 right-0"
        @click="openDiscard"
      >
        <Icon name="icons/delete" :size="16" />
      </Button> -->
      <Icon
        :name="itemAssetNames[inventoryItem.item_type]"
        :size="97"
        class="mx-auto mb-3"
      />
      <div
        class="rounded-[6px] w-max mx-auto py-2 px-4 bg-[#09090980]"
        v-html="
          t('INVENTORY_DETAIL_OWNED', {
            QUANTITY: inventoryItem.quantity,
          })
        "
      ></div>
      <div class="my-5" v-html="t('INVENTORY_DETAIL_DESC')"></div>

      <div class="flex items-center gap-4 w-full flex-nowrap">
        <Button
          class="!w-max !min-w-[auto]"
          variant="purple"
          :label="t('INVENTORY_DETAIL_BTN_HOW_TO_USE')"
          @click="openHowToUse"
        />
        <Button
          class="flex-1"
          :label="t('INVENTORY_DETAIL_BTN_USE')"
          :disable="disabled"
          @click="handleUse"
        />
      </div>
    </div>
  </Dialog>
</template>
<style lang="scss" scoped>
.max-stack {
  background: linear-gradient(
    180deg,
    rgba(197, 54, 54, 0.64) 0%,
    rgba(215, 47, 47, 0.8) 65.22%
  );
}
</style>
