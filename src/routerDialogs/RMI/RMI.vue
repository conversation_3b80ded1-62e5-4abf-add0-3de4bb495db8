<script setup lang="ts">
import { Swiper as ISwiper } from 'swiper/types';
import { useBrandActions, useRMI } from '@composables';
import { BrandActionItem } from '@components';
import { EffectCoverflow, Autoplay } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/vue';
import { RmiAds, RmiOutlet } from '@types';
import { RMI } from '@repositories';
import { useMapStore, useUserStore } from '@stores';
import RMINearBy from './RMINearBy.vue';
import distance from '@turf/distance';
import gsap from 'gsap';

interface Props {
  data: RmiOutlet;
}

const props = defineProps<Props>();

const storeMap = useMapStore();
const storeUser = useUserStore();
const brandHooks = useBrandActions();

const { isEnabledGPS } = storeToRefs(storeUser);
const { lastLocations } = storeToRefs(storeMap);
const { openDialog } = useMicroRoute();
const { t } = useI18n();
const { currentDay, brandByUniqueId, minReward, minRewardData } = useRMI();

const modules = [EffectCoverflow, Autoplay];

const mySwiper = ref<ISwiper | undefined>();
const expanded = ref(false);
const rmiAds = ref<RmiAds[]>([]);

const onSwiper = (swiper: ISwiper) => {
  mySwiper.value = swiper;
};

// const filteredDailyMissions = computed(() => {
//   return dailyMissions.value.filter((mission) => !mission.claimed_at);
// });

const distanceInMeters = computed(() => {
  if (!isEnabledGPS.value || !props.data) return 'N/A';
  const d = distance(
    lastLocations.value,
    [props.data.location.lng, props.data.location.lat],
    { units: 'meters' }
  );
  if (d < 100) return `~${d.toFixed(1)}m away`;
  return `~${(d / 1000).toFixed(1)}km away`;
});

const openingHourKeys = computed(() => {
  if (!props.data || !props.data.open_hours) return [];
  return Object.keys(props.data.open_hours).filter((key) => {
    return props.data.open_hours[key as keyof RmiOutlet['open_hours']] || 'N/A';
  }) as Array<keyof RmiOutlet['open_hours']>;
});

async function getRMIAds() {
  try {
    const { data } = await RMI.getAds();
    rmiAds.value = data;
  } catch (error) {
    console.error('error', error);
  }
}

const randomDescription = computed(() => {
  if (!props.data) return '';
  return props.data.description[
    Math.floor(Math.random() * props.data.description.length)
  ];
});

const scrollToBA = () => {
  const data = minRewardData.value[props.data.brand_unique_id];
  if (!data) return;
  console.log('');

  const element = document.getElementById(`${data._id}`);
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' });
  }
};

onMounted(async () => {
  await getRMIAds();
  gsap.to('.badge', {
    opacity: 1,
    duration: 0.5,
    delay: 0.5,
  });
});

onBeforeUnmount(() => {
  gsap.killTweensOf('.badge');
});
</script>

<template>
  <DbsDialog class="nearby-outlets-detail">
    <template #badge>
      <div
        class="bg-[#6F1190] px-[5px] rounded-[5px] border border-solid border-[#BA69D7] items-center flex w-max absolute z-10 left-1/2 -translate-x-1/2 top-[calc(15%-55px)] badge opacity-0"
        v-if="minReward[data.brand_unique_id]"
        @click="scrollToBA"
      >
        <Icon name="dbs_crystal" :size="16" />
        &nbsp;

        <div
          class="text-sm"
          :class="{
            'line-through text-[#99A0AE]':
              minReward[data.brand_unique_id].haveMultiplier,
          }"
          v-html="minReward[data.brand_unique_id].reward"
        ></div>
        <div
          class="text-sm ml-1"
          v-if="minReward[data.brand_unique_id].haveMultiplier"
          v-html="minReward[data.brand_unique_id].finalReward"
        ></div>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="13"
          height="14"
          viewBox="0 0 13 14"
          fill="none"
        >
          <path
            d="M2.5 11L6.5 7L10.5 11"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M2.5 7L6.5 3L10.5 7"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </template>
    <template v-if="data.images.length">
      <Swiper
        :slides-per-view="1.5"
        :space-between="-10"
        :modules="modules"
        effect="coverflow"
        :autoplay="
          data.images.length > 2
            ? {
                delay: 3000,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              }
            : false
        "
        :coverflow-effect="{
          rotate: 0,
          stretch: 0,
          depth: 100,
          modifier: 2.5,
          slideShadows: false,
        }"
        :centered-slides="true"
        :loop="data.images.length > 1"
        class="pb-10 mb-8 triple-slider"
        @swiper="onSwiper"
      >
        <SwiperSlide v-for="src in data.images" :key="src">
          <div
            class="slide-card"
            @click="
              openDialog('rmi_preview_image', {
                src,
                data,
              })
            "
          >
            <div class="slide-content">
              <MediaRenderer :src="src" />
            </div>
          </div>
        </SwiperSlide>
      </Swiper>
    </template>

    <div class="flex items-center gap-2 justify-between mb-2">
      <div class="flex items-start flex-nowrap">
        <div class="text-base font-bold" v-html="t(data.name)"></div>
        <div class="flex flex-center rounded-[5px] ml-2 px-[5px] py-[3px]">
          <img :src="data.badge" class="!w-[50px]" />
          <q-tooltip
            anchor="top middle"
            self="bottom middle"
            :offset="[10, 10]"
            class="custom-tooltip"
          >
            <div class="tooltip-content">
              <span v-html="t(`rmi_${data.badge_id}`)"></span>
              <div class="tooltip-arrow"></div>
            </div>
          </q-tooltip>
        </div>
      </div>

      <div
        class="bg-[#6F1190] px-[5px] rounded-[5px] border border-solid border-[#BA69D7] items-center flex"
        v-if="minReward[data.brand_unique_id]"
        @click="scrollToBA"
      >
        <Icon name="dbs_crystal" :size="16" />
        &nbsp;

        <div
          class="text-sm"
          :class="{
            'line-through text-[#99A0AE]':
              minReward[data.brand_unique_id].haveMultiplier,
          }"
          v-html="minReward[data.brand_unique_id].reward"
        ></div>
        <div
          class="text-sm ml-1"
          v-if="minReward[data.brand_unique_id].haveMultiplier"
          v-html="minReward[data.brand_unique_id].finalReward"
        ></div>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="13"
          height="14"
          viewBox="0 0 13 14"
          fill="none"
        >
          <path
            d="M2.5 11L6.5 7L10.5 11"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M2.5 7L6.5 3L10.5 7"
            stroke="white"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
    </div>
    <div class="flex items-center gap-1">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="10"
        height="14"
        viewBox="0 0 10 14"
        fill="none"
      >
        <path
          d="M4.84082 0.384766C7.51408 0.384833 9.68143 2.75407 9.68164 5.67676C9.68164 8.59968 7.26117 10.97 4.84082 13.6162C2.42047 10.97 0 8.59968 0 5.67676C0.000215542 2.75403 2.1675 0.384766 4.84082 0.384766ZM4.84082 3.09473C3.58938 3.09473 2.5752 4.17272 2.5752 5.50293C2.57526 6.83308 3.58943 7.91113 4.84082 7.91113C6.09214 7.91105 7.10638 6.83303 7.10645 5.50293C7.10645 4.17277 6.09219 3.09481 4.84082 3.09473Z"
          fill="white"
        />
      </svg>
      <div class="text-xs" v-html="distanceInMeters"></div>
      <div class="text-xs" v-html="`• ${t(data.address)}`"></div>
    </div>
    <div class="mb-3">
      <div class="flex items-center gap-3" @click="expanded = !expanded">
        <div class="text-xs">
          <span v-if="expanded" v-html="t('RMI_LANDMARK_OPEN_HOURS')"></span>
          <span
            v-else
            v-html="
              `${t('RMI_LANDMARK_OPEN_HOURS')} ${
                data.open_hours[currentDay] || '-'
              }`
            "
          >
          </span>
        </div>
        <Icon
          name="arrow-down"
          class="transition-transform duration-300"
          :size="14"
          :class="{ 'rotate-180': expanded }"
        />
      </div>
      <div
        class="overflow-hidden transition-all duration-300 ease-in-out"
        :class="expanded ? 'max-h-28' : 'max-h-0'"
      >
        <div
          class="text-xs flex gap-2 ml-1"
          v-for="day in openingHourKeys"
          :key="day"
        >
          <span class="capitalize" v-html="`${t(day)}:`"></span>
          <span v-html="data.open_hours[day] || '-'"></span>
        </div>
      </div>
    </div>
    <div class="text-xs mb-5" v-html="t(randomDescription)"></div>
    <template v-if="brandByUniqueId[data.brand_unique_id].length">
      <BrandActionItem
        v-for="item in brandByUniqueId[data.brand_unique_id]"
        :id="item._id"
        :key="`brand-action-${item._id}`"
        :data="item"
        :brandHooks="brandHooks"
      />
    </template>
    <template v-if="rmiAds.length">
      <div
        class="mb-4 !mt-[30px] text-left dbs_brand_action_item"
        v-for="item in rmiAds"
        :key="item.unique_id"
      >
        <div
          class="mb-4 ads-banner font-bold"
          v-html="t('RMI_ADS_BANNER')"
        ></div>
        <div class="flex items-center">
          <div style="flex: 1" class="justify-center gap-2 column">
            <div class="text-base font-medium" v-html="t(item.title)"></div>
            <div
              class="text-sm italic font-normal"
              v-html="t(item.description)"
            ></div>
          </div>
          <a :href="item.link" target="_blank" rel="noopener noreferrer">
            <Button
              size="small"
              :label="t('OFFERWALL_BASE_BRANDACTIONACTIONBUTTON')"
            />
          </a>
        </div>
      </div>
    </template>

    <!-- <template v-if="filteredDailyMissions.length">
      <DailyMissionItem
        class="mb-4"
        :class="{
          'mt-[30px]': index === 0,
        }"
        v-for="(mission, index) in filteredDailyMissions"
        :key="mission.unique_id"
        :mission="mission"
      />
    </template> -->
    <RMINearBy :data="data" fromPath="rmi" />
  </DbsDialog>
</template>

<style lang="scss">
.custom-tooltip {
  background: transparent !important;

  .tooltip-content {
    background: linear-gradient(87deg, #bc18d7 72.99%, #a150f1 182.11%);
    border-radius: 8px;
    padding: 8px 12px;
    position: relative;
    color: white;
    border: #ba69d7 1px solid;
    .tooltip-arrow {
      position: absolute;
      bottom: -8px;
      left: 50%;
      transform: translateX(-10%);
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 8px solid #ba69d7;
      &::before {
        content: '';
        position: absolute;
        bottom: 1.5px; // Offset to show border
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 9px solid transparent;
        border-right: 9px solid transparent;
        border-top: 9px solid #bc18d7; // Border color
        z-index: -1;
      }
    }
  }
}
.nearby-outlets-detail {
  .dbs_brand_action_item {
    background: linear-gradient(0deg, #320b5b, #320b5b), #51178c;
    border: 2px solid #51178c;
    background-size: 100% 100% !important;
    width: 100%;
    padding: 10px;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 80%;
      height: 12px;
      left: -5px;
      transform: skew(25deg);
      bottom: -14px;
      background: linear-gradient(180deg, #804ebe 0%, #5a2998 100%);
      border-radius: 2px;
    }
    &::after {
      content: '';
      position: absolute;
      width: 80%;
      height: 12px;
      right: -5px;
      transform: skew(-25deg);
      bottom: -14px;
      background: linear-gradient(180deg, #804ebe 0%, #5a2998 100%);
      border-radius: 2px;
    }
  }
  .slide-container {
    .slide-item {
      padding: 0 10px;
      background-repeat: no-repeat;
      border-radius: 8px;

      .slide-content {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #6f1190, #ba69d7);
        border-radius: 8px;
        color: white;
      }
    }

    .swiper-wrapper {
      padding-bottom: 5px;
    }

    // Style for active slide when centered
    .swiper-slide-active .slide-item {
      transform: scale(1.05);
      transition: transform 0.3s ease;
    }
  }

  .triple-slider {
    width: 100%;
    overflow: visible;

    .swiper-slide {
      background-position: center;
      background-size: cover;
      transition: all 0.3s ease;

      .slide-card {
        width: 100%;
        height: 100%;
        border-radius: 5px;
        overflow: hidden;
        cursor: pointer;
        transform: scale(0.8);
        opacity: 0.7;
        transition: all 0.5s ease;

        .slide-content {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          color: white;
          text-align: center;
          position: relative;
        }
      }

      // Active slide styling
      &.swiper-slide-active {
        .slide-card {
          transform: scale(1);
          opacity: 1;
        }
      }

      // Previous and next slides
      &.swiper-slide-prev,
      &.swiper-slide-next {
        .slide-card {
          transform: scale(0.85);
          opacity: 0.8;
        }
      }
    }

    // Pagination styling
    .swiper-pagination {
      bottom: 0;

      .swiper-pagination-bullet {
        width: 15px;
        height: 15px;
        background: rgba(255, 255, 255, 0.5);
        opacity: 1;
        margin: 0 8px;
        transition: all 0.3s ease;

        &.swiper-pagination-bullet-active {
          background: #fff;
          transform: scale(1.2);
          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}

.ads-banner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 35px 5px 20px;
  margin-top: -10px;
  margin-left: -20px;
  width: max-content;
  height: 35px;
  background: url(/imgs/banner-red-offer.png);
  background-size: 100% 100%;
}
</style>
