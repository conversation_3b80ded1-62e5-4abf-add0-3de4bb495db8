<script setup lang="ts">
import { useMapStore, useUserStore } from '@stores';
import { RmiOutlet } from '@types';
import distance from '@turf/distance';
import dayjs from 'dayjs';
import RMINearBy from './RMINearBy.vue';

interface Props {
  data: RmiOutlet;
}

const props = defineProps<Props>();

const DAYS_OF_WEEK = [
  'sunday',
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
] as const;

const storeMap = useMapStore();
const storeUser = useUserStore();

const { isEnabledGPS } = storeToRefs(storeUser);
const { lastLocations } = storeToRefs(storeMap);
const { t } = useI18n();
const { openDialog } = useMicroRoute();

const expanded = ref(false);
const slide = ref(0);

const currentDay = computed(() => {
  const day = dayjs().day();
  return DAYS_OF_WEEK[day] as keyof RmiOutlet['open_hours'];
});

const distanceInMeters = computed(() => {
  if (!isEnabledGPS.value || !props.data) return 'N/A';
  const d = distance(
    lastLocations.value,
    [props.data.location.lng, props.data.location.lat],
    { units: 'meters' }
  );
  if (d < 100) return `~${d.toFixed(1)}m away`;
  return `~${(d / 1000).toFixed(1)}km away`;
});

const openingHourKeys = computed(() => {
  if (!props.data || !props.data.open_hours) return [];
  return Object.keys(props.data.open_hours).filter((key) => {
    return props.data.open_hours[key as keyof RmiOutlet['open_hours']] || 'N/A';
  }) as Array<keyof RmiOutlet['open_hours']>;
});

const randomDidYouKnow = computed(() => {
  if (!props.data) return '';
  return props.data.did_you_know[
    Math.floor(Math.random() * props.data.did_you_know.length)
  ];
});

const randomDescription = computed(() => {
  if (!props.data) return '';
  return props.data.description[
    Math.floor(Math.random() * props.data.description.length)
  ];
});
</script>
<template>
  <DbsDialog>
    <div class="flex justify-between flex-nowrap mb-4 gap-5">
      <div class="flex flex-col gap-1">
        <div class="text-lg font-bold" v-html="t(data.name)"></div>
        <div class="flex items-center gap-1">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="10"
            height="14"
            viewBox="0 0 10 14"
            fill="none"
          >
            <path
              d="M4.84082 0.384766C7.51408 0.384833 9.68143 2.75407 9.68164 5.67676C9.68164 8.59968 7.26117 10.97 4.84082 13.6162C2.42047 10.97 0 8.59968 0 5.67676C0.000215542 2.75403 2.1675 0.384766 4.84082 0.384766ZM4.84082 3.09473C3.58938 3.09473 2.5752 4.17272 2.5752 5.50293C2.57526 6.83308 3.58943 7.91113 4.84082 7.91113C6.09214 7.91105 7.10638 6.83303 7.10645 5.50293C7.10645 4.17277 6.09219 3.09481 4.84082 3.09473Z"
              fill="white"
            />
          </svg>
          <div class="text-xs">{{ distanceInMeters }}</div>
          <div class="text-xs" v-html="`• ${t(data.address)}`"></div>
        </div>
        <div class="flex items-center gap-3 p-1" @click="expanded = !expanded">
          <div class="text-xs">
            <span v-if="expanded" v-html="t('RMI_LANDMARK_OPEN_HOURS')"></span>
            <span
              v-else
              v-html="
                `${t('RMI_LANDMARK_OPEN_HOURS')} ${
                  data.open_hours[currentDay] || '-'
                }`
              "
            >
            </span>
          </div>
          <Icon
            name="arrow-down"
            class="transition-transform duration-300"
            :size="14"
            :class="{ 'rotate-180': expanded }"
          />
        </div>
        <div
          class="overflow-hidden transition-all duration-300 ease-in-out"
          :class="expanded ? 'max-h-28' : 'max-h-0'"
        >
          <div
            class="text-xs flex gap-2 ml-1"
            v-for="day in openingHourKeys"
            :key="day"
          >
            <span class="capitalize" v-html="`${t(day)}:`"></span>
            <span v-html="data.open_hours[day] || '-'"></span>
          </div>
        </div>
      </div>
      <q-carousel
        animated
        v-model="slide"
        :infinite="data.images.length > 1"
        :swipeable="data.images.length > 1"
        class="rounded-md border border-teal-100 !bg-transparent size-[100px]"
      >
        <q-carousel-slide
          v-for="(src, index) in data.images"
          :key="index"
          :name="index"
          class="p-0 h-full"
          @click="
            openDialog('rmi_preview_image', {
              src,
              data,
            })
          "
        >
          <q-img class="h-full object-cover rounded-md !min-w-10" :src="src" />
        </q-carousel-slide>
      </q-carousel>
    </div>
    <div class="text-sm mb-4" v-html="t(randomDescription)"></div>
    <q-expansion-item
      class="rmi-expansion mb-4"
      :expand-icon="`img:imgs/arrow-down.png`"
      expand-icon-class="arrow-expand"
      :label="t('RMI_LANDMARK_DID_YOU_KNOW')"
    >
      <q-card class="bg-[#091a3b]">
        <q-card-section class="text-sm">
          <span v-html="t(randomDidYouKnow)"></span>
        </q-card-section>
      </q-card>
    </q-expansion-item>
    <RMINearBy :data="data" fromPath="rmi_landmark" />
  </DbsDialog>
</template>
<style lang="scss">
.rmi-expansion {
  border-radius: 4px;
  border: 1px solid rgba(54, 76, 118, 0.4);
  background: #091a3b;
  .q-expansion-item__container {
    .q-item__section {
      .q-item__label {
        font-weight: bold !important;
      }
    }
  }
}
</style>
