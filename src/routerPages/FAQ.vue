<script setup lang="ts">
import { useUserStore } from '@stores';
import { useClick } from '@composables';

const storeUser = useUserStore();

const { initialSlide } = storeToRefs(storeUser);
const { t } = useI18n();
const { push, openDialog } = useMicroRoute();

const search = ref('');
const select = ref('');

interface IFAQ {
  header: string;
  list: {
    question: string;
    answer: string;
  }[];
}

const CONTENTS = computed<IFAQ[]>(() => {
  return [
    {
      header: t('FAQ_ABOUTTHEGAME'),
      list: [
        {
          question: t('FAQ_ABOUTTHEGAME_QUESTION1_HEADING'),
          answer: t('FAQ_ABOUTTHEGAME_QUESTION1_DESCRIPTION'),
        },
        {
          question: t('FAQ_ABOUTTHEGAME_QUESTION2_HEADING'),
          answer: t('FAQ_ABOUTTHEGAME_QUESTION2_DESCRIPTION'),
        },
        {
          question: t('FAQ_ABOUTTHEGAME_QUESTION3_HEADING'),
          answer: t('FAQ_ABOUTTHEGAME_QUESTION3_DESCRIPTION'),
        },
        {
          question: t('FAQ_ABOUTTHEGAME_QUESTION4_HEADING'),
          answer: t('FAQ_ABOUTTHEGAME_QUESTION4_DESCRIPTION'),
        },
        {
          question: t('FAQ_ABOUTTHEGAME_QUESTION5_HEADING'),
          answer: t('FAQ_ABOUTTHEGAME_QUESTION5_DESCRIPTION'),
        },
        {
          question: t('FAQ_ABOUTTHEGAME_QUESTION6_HEADING'),
          answer: t('FAQ_ABOUTTHEGAME_QUESTION6_DESCRIPTION'),
        },
      ],
    },
    {
      header: t('FAQ_ACCOUNTCREATION'),
      list: [
        {
          question: t('FAQ_ACCOUNTCREATION_QUESTION1_HEADING'),
          answer: t('FAQ_ACCOUNTCREATION_QUESTION1_DESCRIPTION'),
        },
        {
          question: t('FAQ_ACCOUNTCREATION_QUESTION2_HEADING'),
          answer: t('FAQ_ACCOUNTCREATION_QUESTION2_DESCRIPTION'),
        },
        {
          question: t('FAQ_ACCOUNTCREATION_QUESTION3_HEADING'),
          answer: t('FAQ_ACCOUNTCREATION_QUESTION3_DESCRIPTION'),
        },
      ],
    },
    {
      header: t('FAQ_HOWTOHUNT'),
      list: [
        {
          question: t('FAQ_HOWTOHUNT_QUESTION1_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION1_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION2_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION2_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION3_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION3_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION4_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION4_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION5_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION5_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION6_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION6_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION7_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION7_DESCRIPTION'),
        },
        {
          question: t('FAQ_HOWTOHUNT_QUESTION8_HEADING'),
          answer: t('FAQ_HOWTOHUNT_QUESTION8_DESCRIPTION'),
        },
      ],
    },
    {
      header: t('FAQ_GETMORE'),
      list: [
        {
          question: t('FAQ_GETMORE_QUESTION1_HEADING'),
          answer: t('FAQ_GETMORE_QUESTION1_DESCRIPTION'),
        },
        {
          question: t('FAQ_GETMORE_QUESTION2_HEADING'),
          answer: t('FAQ_GETMORE_QUESTION2_DESCRIPTION'),
        },
        {
          question: t('FAQ_GETMORE_QUESTION3_HEADING'),
          answer: t('FAQ_GETMORE_QUESTION3_DESCRIPTION'),
        },
        {
          question: t('FAQ_GETMORE_QUESTION4_HEADING'),
          answer: t('FAQ_GETMORE_QUESTION4_DESCRIPTION'),
        },
      ],
    },
    {
      header: t('FAQ_REFERRAL'),
      list: [
        {
          question: t('FAQ_REFERRAL_QUESTION1_HEADING'),
          answer: t('FAQ_REFERRAL_QUESTION1_DESCRIPTION'),
        },
        {
          question: t('FAQ_REFERRAL_QUESTION2_HEADING'),
          answer: t('FAQ_REFERRAL_QUESTION2_DESCRIPTION'),
        },
        {
          question: t('FAQ_REFERRAL_QUESTION3_HEADING'),
          answer: t('FAQ_REFERRAL_QUESTION3_DESCRIPTION'),
        },
      ],
    },
    {
      header: t('FAQ_SUPPORT'),
      list: [
        {
          question: t('FAQ_SUPPORT_QUESTION1_HEADING'),
          answer: t('FAQ_SUPPORT_QUESTION1_DESCRIPTION'),
        },
        {
          question: t('FAQ_SUPPORT_QUESTION2_HEADING'),
          answer: t('FAQ_SUPPORT_QUESTION2_DESCRIPTION'),
        },
        {
          question: t('FAQ_SUPPORT_QUESTION3_HEADING'),
          answer: t('FAQ_SUPPORT_QUESTION3_DESCRIPTION'),
        },
        {
          question: t('FAQ_SUPPORT_QUESTION4_HEADING'),
          answer: t('FAQ_SUPPORT_QUESTION4_DESCRIPTION'),
        },
        {
          question: t('FAQ_SUPPORT_QUESTION5_HEADING'),
          answer: t('FAQ_SUPPORT_QUESTION5_DESCRIPTION'),
        },
        {
          question: t('FAQ_SUPPORT_QUESTION6_HEADING'),
          answer: t('FAQ_SUPPORT_QUESTION6_DESCRIPTION'),
        },
      ],
    },
  ];
});

const options = computed(() => CONTENTS.value.map((c) => c.header));

const selectedContents = computed(() => {
  if (select.value && ![t('FAQ_ALLCATEGORIES')].includes(select.value)) {
    return CONTENTS.value.filter((c) => c.header === select.value);
  }
  return CONTENTS.value;
});

const filteredContents = computed(() => {
  const searchRegex = new RegExp(search.value, 'i');
  return selectedContents.value.filter(
    (section) =>
      section.header.match(searchRegex) ||
      section.list.some(
        (item) =>
          item.answer.match(searchRegex) || item.question.match(searchRegex)
      )
  );
});

useClick('TAC', () => {
  openDialog('tac');
});

useClick('goTimeline', () => {
  openDialog('time_line_v2', {
    defaultInitialSlide: initialSlide.value + 1,
  });
});

useClick('crystals', () => {
  push('offer_wall');
});
</script>

<template>
  <div class="faq">
    <Button
      shape="square"
      class="mb-[25px]"
      variant="secondary"
      @click="push(-1)"
    >
      <Icon name="arrow-left" />
    </Button>
    <div
      class="absolute text-center top-4 left-1/2 -translate-x-1/2 text-lg font-bold"
      v-html="t('FAQ_HEADING')"
    ></div>
    <div class="px-3">
      <Input
        class="full-width input-search mb-5"
        type="search"
        :label="t('FAQ_SEARCH')"
        v-model="search"
      />
      <Select
        class="mb-4"
        v-model="select"
        :label="t('FAQ_ALLCATEGORIES')"
        :options="options"
        type="secondary"
      />
    </div>
    <div class="faq-content">
      <Expansion
        v-for="{ header, list } in filteredContents"
        :key="header"
        switch-toggle-side
        expand-separator
        group="faq"
      >
        <template #header>
          <div class="text-xl font-bold w-[90%]" v-html="header"></div>
        </template>
        <div class="px-5">
          <div
            class="faq-card"
            v-for="{ question, answer } in list"
            :key="question"
          >
            <div class="text-lg font-semibold mb-2" v-html="question"></div>
            <div class="text-sm" v-html="answer"></div>
          </div>
        </div>
      </Expansion>
    </div>
  </div>
</template>

<style lang="scss">
.faq {
  position: relative;
  background-color: #090422;
  padding: 10px;
  display: flex;
  flex-flow: column;
  width: 100%;
  height: 100%;
  .select-search {
    .q-field__label {
      color: #ffffff;
      opacity: 0.5;
    }
    .q-field__inner {
      z-index: 9;
      background: #2e3b54;
      border-radius: 4px;
    }

    .q-field__native {
      color: #ffffff;
    }
    .q-chip {
      background: #6e60cb;
      color: #ffffff;
    }
  }

  &-content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    .faq-card {
      background: linear-gradient(
        178.55deg,
        rgba(37, 25, 109, 0.95) 1.24%,
        rgba(29, 65, 137, 0.9) 46.04%
      );
      border-radius: 4px;
      padding: 20px 12px;
      &:not(:last-child) {
        margin-bottom: 20px;
      }
    }
    li {
      margin-bottom: 10px;
    }
  }
}
</style>
