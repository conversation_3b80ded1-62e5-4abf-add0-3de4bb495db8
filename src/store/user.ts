import { CAPITALAND } from '@repositories/capitaland';
import { defineStore } from 'pinia';
import { Cookies, LocalStorage } from 'quasar';

import { Socket } from 'socket.io-client';
import {
  USER,
  TIMELINE,
  EVENT,
  REFERRAL,
  HINT,
  DAILYMISSION,
  BEACON,
  SENTOSA,
  INVENTORY,
} from '@repositories';
import type {
  ISeason,
  IUser,
  IUserSetting,
  ISettings,
  ITimeline,
  IAdventureLog,
  IEvent,
  IContest,
  IReferral,
  IContestList,
  IVerification,
  TSeasonISO,
  INotification,
  IMetalSonarEvent,
  IHint,
  IHintState,
  IDailyMission,
  PedometerProgress,
  TOnboarding,
  IBeacon,
  ITransaction,
  ICrystalExpiring,
  IUserSettingPayload,
  SentosaDailyReward,
  SentosaGoldenCoin,
  ISentosaIslandBounty,
  IAmenities,
  IventoryData,
} from '@types';
import { useMapHelpers } from '@composables';

import dayjs from 'dayjs';

interface IUserState {
  token: string | null;
  user: IUser | null;
  socketIO: {
    socket?: Socket;
    authenticated?: boolean;
  };
  currentSeason: ISeason | null;
  settings: ISettings | null;
  timelines: ITimeline[];
  adventureLogs: IAdventureLog[];
  isEnabledGPS: boolean;
  hasTriggerGPS: boolean;
  currentCountryCode: string;
  seasonCode: TSeasonISO;
  events: IEvent | null;
  contest: IContest | null;
  contestList: IContestList;
  referrals: IReferral[];
  dataVerification: IVerification | null;
  notifications: INotification[];
  userTracked: boolean;
  pedometerProgress: PedometerProgress;
  metalSonarEvent: IMetalSonarEvent | null;
  hints: IHint[];
  hintState: IHintState | null;
  dailyMissions: IDailyMission[];
  activatingBeacons: IBeacon[];
  transactions: ITransaction[];
  listCrystalExpiring: ICrystalExpiring[];
  metalDetectorItemId: string;
  visitCounting: number;
  merchantAcquisition: {
    visited_sv: number;
    closed_callout: number;
    max_submit: number;
    total_submit: number;
    show_callout: boolean;
    last_type: 'friends' | 'owners';
    submitted: boolean;
  };
  currentStep: number;
  tempStepsWalking: number;
  stepUpdated: boolean;
  fetchedSettings: boolean;
  sentosaDailyRewards: SentosaDailyReward[][];
  sentosaGoldenCoin: SentosaGoldenCoin[];
  crystalDetectorItemId: string;
  currentCrystalCoinUpdate: SentosaGoldenCoin | null;
  sentosaBeachCount: number;
  sentosaBeachPolygon: any;
  sentosaIslandBounty: ISentosaIslandBounty | null;
  capitalandDailyRewards: SentosaDailyReward[][];
  capitalandGeneoCoin: SentosaGoldenCoin[];
  capitalandAmenities: IAmenities[];
  inventoryData: IventoryData;
}

export const useUserStore = defineStore('user', {
  state: (): IUserState => ({
    token: LocalStorage.getItem(`${process.env.APP_NAME}_TOKEN`) || null,
    user: null,
    socketIO: {},
    settings: null,
    currentSeason: null,
    timelines: [],
    adventureLogs: [],
    isEnabledGPS: !!LocalStorage.getItem('enable_gps'),
    hasTriggerGPS: !!LocalStorage.has('enable_gps'),
    currentCountryCode: '',
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    seasonCode: process.env.APP_COUNTRY_CODE || '',
    events: null,
    contest: null,
    contestList: {
      contests: [],
      user_contests: [],
    },
    referrals: [],
    dataVerification: null,
    notifications: [],
    userTracked: false,
    metalSonarEvent: null,
    hints: [],
    hintState: null,
    dailyMissions: [],
    pedometerProgress: [],
    activatingBeacons: [],
    transactions: [],
    listCrystalExpiring: [],
    metalDetectorItemId: '',
    visitCounting: 0,
    merchantAcquisition: {
      visited_sv: Number(
        LocalStorage.getItem(
          `${process.env.APP_NAME}_SQKII_VOUCHER_VISITED_SV`
        ) || 0
      ),
      closed_callout: Number(
        LocalStorage.getItem(
          `${process.env.APP_NAME}_SQKII_VOUCHER_CLOSED_CALLOUT`
        ) || 0
      ),
      max_submit: 0,
      total_submit: 0,
      show_callout: false,
      last_type: String(
        LocalStorage.getItem(
          `${process.env.APP_NAME}_SQKII_VOUCHER_LAST_TYPE`
        ) || 'owners'
      ) as 'friends' | 'owners',
      submitted: false,
    },
    currentStep: 0,
    tempStepsWalking: 0,
    stepUpdated: false,
    fetchedSettings: false,
    sentosaDailyRewards: [],
    sentosaGoldenCoin: [],
    crystalDetectorItemId: '',
    currentCrystalCoinUpdate: null,
    sentosaBeachCount: 0,
    sentosaBeachPolygon: null,
    sentosaIslandBounty: null,
    capitalandDailyRewards: [],
    capitalandGeneoCoin: [],
    capitalandAmenities: [],
    inventoryData: {
      items: [],
      inventory_info: {
        extended_slots: 0,
        max_inventory_size: 0,
        max_stack_size: 0,
      },
    },
  }),

  getters: {
    isAuthenticated: (state) => {
      return !!state.token;
    },
    isReturnedUser: (state) => {
      return (
        state.user &&
        state.settings &&
        (state.user.registered_at || state.user?.created_at) >
          state.settings.dbs.start_at
      );
    },

    onboarding: (state) => {
      return state.user?.onboarding;
    },

    isSeasonStarting: (state) => {
      if (!state.currentSeason || !state.user) return false;
      return state.currentSeason.status === 'ongoing';
    },

    isDifferentCountry: (state) => {
      if (Boolean(process.env.IS_TESTING_ENV)) return false;
      return state.currentCountryCode !== state.seasonCode;
    },

    hasGoldenCoin: (state) => {
      if (!state.settings || !state.currentSeason?.features?.hints)
        return false;
      return !!state.settings?.golden_coin?.status;
    },

    isFoundGoldenCoin: (state) => {
      if (!state.settings) return false;
      return (
        !!state.settings.golden_coin &&
        state.settings.golden_coin.status === 'found' &&
        !!state.settings.golden_coin.geohash
      );
    },

    isVerifyingGoldenCoin: (state) => {
      if (!state.settings) return false;
      return (
        !!state.settings.golden_coin &&
        state.settings.golden_coin.status === 'verifying'
      );
    },

    goldenCoinDropped: (state) => {
      if (!state.settings) return false;
      return (
        !!state.settings.golden_coin &&
        state.settings.golden_coin.status === 'ongoing'
      );
    },

    goldenCoinScheduled: (state) => {
      if (!state.settings) return false;
      return (
        !!state.settings.golden_coin &&
        state.settings.golden_coin.status === 'scheduled'
      );
    },

    crystals: (state) => {
      if (!state.user) return 0;
      return state.user.resources.dbs_crystal;
    },

    baseCrystals: (state) => {
      if (!state.user) return 0;
      return state.user.resources.crystal;
    },

    features: (state) => {
      return state.settings?.features;
    },

    endGame: (state) => {
      return !!state.settings?.flags?.all_coins_found;
    },

    canTriggerEndGameSurvey: (state) => {
      return !!state.settings?.flags?.end_game_survey;
    },

    perpetualHunt: (state) => {
      return !!state.settings?.flags?.perpetual_hunt;
    },

    lastTenGrids: (state) => {
      return !!state.settings?.flags?.last_10_grids;
    },

    beacons: (state) => {
      return state.user?.resources.beacon || 0;
    },

    initialSlide: (state) => {
      if (!state.timelines.length) return 0;
      return state.timelines.findIndex((t) => t.status === 'ongoing') || 0;
    },

    isTriggerSurvey(state) {
      if (!state.user || !state.currentSeason) return false;
      if (
        state.currentSeason.status !== 'ongoing' ||
        !state.currentSeason.features.survey ||
        !!state.user.survey
      )
        return false;
      if (+new Date(state.currentSeason.start_at) > +new Date()) return false;

      const today = dayjs();
      const startHunt = dayjs(state.currentSeason.start_at);
      const joinedHunt = dayjs(state.user.created_at);
      const daysSinceJoin = today.diff(joinedHunt, 'day');
      const daysSinceHunt = today.diff(startHunt, 'day');

      if (daysSinceHunt < 2) return false;
      return daysSinceJoin >= 3;
    },

    referralsById(state) {
      return state.referrals.reduce<Record<string, IReferral>>((r, a) => {
        r[a.id] = a;
        return r;
      }, {});
    },

    crystalsDetector(state) {
      return state.user?.resources.sentosa_crystal_detector || 0;
    },

    nextCrystalCoin(state) {
      return state.capitalandGeneoCoin
        .filter((c) => c.status === 'scheduled')
        .at(0);
    },
    endSentosaGame(state) {
      return (
        !!state.settings?.flags?.crystal_coin_end &&
        !!state.settings?.flags.sentosa_coin_end
      );
    },

    endCapitalandGame(state) {
      return (
        !!state.settings?.flags?.crystal_coin_end &&
        !!state.settings?.flags.capita_land_coin_end
      );
    },
  },

  actions: {
    setUser(user: IUser) {
      this.user = user;
    },

    setToken(token: string) {
      this.token = token;
      LocalStorage.set(`${process.env.APP_NAME}_TOKEN`, token);
    },

    setSocket(data: { socket?: Socket; authenticated?: boolean }) {
      this.socketIO = { ...this.socketIO, ...data };
    },

    async logout() {
      LocalStorage.clear();
      location.reload();
    },

    async playAsGuest() {
      try {
        const { data } = await USER.guest();
        this.setUser(data.user);
        this.setToken(data.token);
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchUser() {
      try {
        const { data } = await USER.getUser();
        this.user = data;
        if (this.user.sv_token)
          LocalStorage.set(
            `${process.env.APP_NAME}_SQKII_VOUCHER_TOKEN`,
            this.user.sv_token
          );
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchSetting() {
      try {
        const { data } = await USER.getSettings();

        // for holding state
        const produtionLinks = {
          VN: 'https://vn.huntthemouse.sqkii.com',
        };

        // const start = dayjs().add(30, 'seconds').toISOString();

        this.settings = {
          ...data,
          holding_page_target_url:
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            produtionLinks[this.currentCountryCode] || '',
          dates: {
            ...data.dates,
            // sentosa_beach_station_event_start_at: start,
            // sentosa_beach_station_event_end_at: dayjs(start)
            //   .add(30, 'second')
            //   .toISOString(),
          },
        };

        if (!this.settings) return;
        if (this.settings.golden_coin?.status === 'found')
          this.goldenCoinGeoHash(data);
        const season = this.settings.seasons.find(
          (s) =>
            +new Date(s.start_at) <= +new Date() &&
            +new Date(s.end_at) > +new Date()
        );
        if (season) this.currentSeason = season;
        this.fetchedSettings = true;
      } catch (error) {
        // console.error('error', error);
      }
    },

    goldenCoinGeoHash(data: ISettings) {
      const { encode } = useMapHelpers();
      const { lat, lng } = data.golden_coin.location;

      const geohash = encode(lat, lng);

      this.settings = {
        ...data,
        golden_coin: {
          ...data.golden_coin,
          geohash,
        },
      };
    },

    async fetchTimeline() {
      try {
        const { data } = await TIMELINE.get();
        this.timelines = data;
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchAdventureLog() {
      try {
        const { data } = await USER.getAdventureLog();
        this.adventureLogs = data;
      } catch (error) {
        // console.error('error', error);
      }
    },

    async updateCountryCode() {
      const { data } = await USER.checkCountryCode();
      this.currentCountryCode = data.country;
      Cookies.set('country_code', data.country, {
        expires: 3,
        sameSite: 'Lax',
      });
    },

    async fetchCountryCode() {
      try {
        const cachedCountryCode = Cookies.get('country_code');
        if (cachedCountryCode) {
          this.currentCountryCode = cachedCountryCode;
          this.updateCountryCode(); // without await
          return;
        }
        await this.updateCountryCode();
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchEvents() {
      try {
        const { data } = await EVENT.list();
        if (!data) return;
        const event_updates = data?.event_updates.map((event) => {
          const userEvent = data?.user_event_updates.find(
            (e) => e.event_update === event._id
          );
          return {
            ...event,
            seen: !!userEvent,
          };
        });
        this.events = {
          event_updates,
          user_event_updates: data?.user_event_updates,
        };
      } catch (error) {
        // console.error('error', error);
      }
    },

    async getContestById(contestId: string) {
      try {
        const { data } = await EVENT.contest(contestId);
        this.contest = data;
      } catch (error) {
        // console.error('error', error);
      }
    },

    async updateOnboarding(type: TOnboarding) {
      try {
        await USER.updateOnboarding({ type });
        if (this.user) this.user.onboarding[type] = true;
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchReferral() {
      try {
        const { data } = await REFERRAL.get();
        this.referrals = data;
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchContest() {
      try {
        const { data } = await EVENT.constestList();
        this.contestList = data;
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchAnnouncement() {
      try {
        const { data } = await USER.getAnn();
        this.notifications = data.filter((d) => d.should_show);
      } catch (error) {
        // console.error('error', error);
      }
    },

    async referralUser(referral_code: string) {
      try {
        if (this.user?.mobile_number) return;
        await REFERRAL.enterReferralCode({
          referral_code,
        });
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchMetalSonarEvent() {
      try {
        const { data } = await EVENT.metalSonarEvent();
        this.metalSonarEvent = data;
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchHints() {
      try {
        const { data } = await HINT.list();
        this.hints = data;
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchDailyMission() {
      try {
        const { data } = await DAILYMISSION.get();
        this.dailyMissions = data
          .map((d) => ({ ...d, toggle: false }))
          .sort((a, b) => (b.progress >= b.required ? 1 : -1))
          .sort((a, b) => (b.claimed_at ? -1 : 1));
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchSentosaBeachCount() {
      try {
        const { data } = await SENTOSA.sentosaBeachCount();
        this.sentosaBeachCount = data.count;
      } catch (error) {
        // console.error('error', error);
      }
    },

    drawSentosaBeachPolygon() {
      const { makeCircleFeature, makeSource } = useMapHelpers();
      const p = makeSource([makeCircleFeature(1.251829, 103.817962, 120)]);
      this.sentosaBeachPolygon = p;
    },

    updateSentosaBeachCount(count: number) {
      this.sentosaBeachCount = count;
    },

    async fetchBeacon() {
      try {
        const { data } = await BEACON.getActivating();
        this.activatingBeacons = data.beacons;
      } catch (error) {
        // console.error('error', error);
      }
    },

    updateBeacon(beacon: IBeacon) {
      const data = {
        ...beacon,
        is_owner: beacon.user === this.user?.id ? 1 : 0,
      };
      this.activatingBeacons.push(data);
    },

    async checkHint() {
      const { data } = await HINT.check();
      this.hintState = data;
    },

    updateUser(fieldsToUpdate: Partial<IUser>) {
      this.user = { ...this.user, ...fieldsToUpdate } as IUser;
    },

    updateSettings(fieldsToUpdate: Partial<ISettings>) {
      if (!this.settings) return;
      this.settings = {
        ...this.settings,
        ...fieldsToUpdate,
      };
    },

    async updateUserSettings(
      type: IUserSettingPayload['type'],
      value: IUserSettingPayload['value']
    ) {
      try {
        await USER.updateUserSettings({
          type: type,
          value: value,
        });
        if (!this.user?.setting) return;
        this.user.setting = {
          ...this.user.setting,
          [type]: value,
        };
      } catch (error) {}
    },

    updateSound(fieldsToUpdate: Partial<IUserSetting>) {
      if (!this.user) return;
      this.user = {
        ...this.user,
        setting: {
          ...this.user.setting,
          ...fieldsToUpdate,
        },
      };
    },

    updateCrystals(crystals: number) {
      if (!this.user) return;
      this.user.resources.crystal += crystals;
    },

    setUserGPS(active: boolean) {
      LocalStorage.set('enable_gps', active);
      this.isEnabledGPS = active;
    },

    setDataVerification(data: IVerification) {
      this.dataVerification = { ...this.dataVerification, ...data };
    },

    setNotifications(notification: INotification) {
      this.notifications = [...this.notifications, notification];
    },

    async fetchPedometerProgress() {
      try {
        const { data } = await USER.getPedometerProgress();
        this.pedometerProgress = data;
        this.currentStep =
          data.find((item) =>
            dayjs(item.date).startOf('day').isSame(dayjs().startOf('day'))
          )?.steps || 0;
      } catch (error) {}
    },

    updatePedometerProgress(pedometerProgress: PedometerProgress) {
      this.pedometerProgress = pedometerProgress;
      this.currentStep =
        pedometerProgress.find((item) =>
          dayjs(item.date).startOf('day').isSame(dayjs().startOf('day'))
        )?.steps || 0;
      this.stepUpdated = true;
    },

    async fetchTranSaction() {
      try {
        const { data } = await USER.getTransaction();
        this.transactions = data;
      } catch (error) {}
    },

    async fetchListCrystalExpiring() {
      try {
        const { data } = await USER.getCrystalExpiring();
        this.listCrystalExpiring = data.crystal_expiring.filter(
          (item) => !!item.expiring_amount
        );
      } catch (error) {}
    },

    async fetchTotalSVSubmitted() {
      try {
        const { data } = await USER.getSVReferral();

        this.merchantAcquisition = {
          ...this.merchantAcquisition,
          max_submit: data.max,
          total_submit: data.current,
        };
        this.checkShowCallout();
      } catch (error) {}
    },

    checkShowCallout() {
      if (
        this.merchantAcquisition.total_submit >=
        this.merchantAcquisition.max_submit
      )
        return (this.merchantAcquisition.show_callout = false);

      if (this.merchantAcquisition.closed_callout >= 3)
        return (this.merchantAcquisition.show_callout = false);

      this.merchantAcquisition.show_callout =
        this.merchantAcquisition.visited_sv >= 3 &&
        Number.isInteger(Math.log2(this.merchantAcquisition.visited_sv / 3));

      if (
        this.merchantAcquisition.show_callout &&
        this.merchantAcquisition.visited_sv > 3
      ) {
        this.merchantAcquisition.last_type =
          this.merchantAcquisition.last_type === 'owners'
            ? 'friends'
            : 'owners';
        LocalStorage.set(
          `${process.env.APP_NAME}_SQKII_VOUCHER_LAST_TYPE`,
          this.merchantAcquisition.last_type
        );
      }
    },

    updateMerchantAcquisition(type: 'visited_sv' | 'closed_callout') {
      const values = Number(
        LocalStorage.getItem(
          `${process.env.APP_NAME}_SQKII_VOUCHER_${type.toUpperCase()}`
        )
      );
      this.merchantAcquisition[type] = values + 1;
      LocalStorage.set(
        `${process.env.APP_NAME}_SQKII_VOUCHER_${type.toUpperCase()}`,
        values + 1
      );
      this.checkShowCallout();
    },

    // for sentosa
    async fetchSentosaDailyRewards() {
      try {
        const { data } = await SENTOSA.dailyRewards();

        const totalCheckedIn = data.filter((item) => item.claimed_at).length;

        const maxDaysInView = 7;
        const currentSlide = Math.floor(totalCheckedIn / maxDaysInView); // floor(9 / 7) => 1
        const endIndex = currentSlide * maxDaysInView + maxDaysInView; // 1 * 7 + 7 => 14
        const dataToShow = data.slice(0, endIndex); // slice(0, 14) = 2 slides

        this.sentosaDailyRewards = dataToShow.reduce<SentosaDailyReward[][]>(
          (acc, reward, index) => {
            const weekIndex = Math.floor(index / 7);
            if (!acc[weekIndex]) acc[weekIndex] = [];
            acc[weekIndex].push(reward);
            return acc;
          },
          []
        );
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchSentosaGoldenCoin() {
      try {
        const { data } = await SENTOSA.sentosaGoldenCoins();

        this.sentosaGoldenCoin = data;
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchSentosaIslandBounty() {
      try {
        const { data } = await SENTOSA.getSentosaIslandBounty();
        this.sentosaIslandBounty = data;
      } catch (error) {
        // console.error('error', error);
      }
    },

    updateSentosaIslandBounty(fieldsToUpdate: Partial<ISentosaIslandBounty>) {
      this.sentosaIslandBounty = {
        ...this.sentosaIslandBounty,
        ...fieldsToUpdate,
      } as ISentosaIslandBounty;
    },

    // CAPITALAND
    async fetchCapitalandDailyRewards() {
      try {
        const { data } = await CAPITALAND.dailyRewards();

        const totalCheckedIn = data.filter((item) => item.claimed_at).length;

        const maxDaysInView = 7;
        const currentSlide = Math.floor(totalCheckedIn / maxDaysInView); // floor(9 / 7) => 1
        const endIndex = currentSlide * maxDaysInView + maxDaysInView; // 1 * 7 + 7 => 14
        const dataToShow = data.slice(0, endIndex); // slice(0, 14) = 2 slides

        this.capitalandDailyRewards = dataToShow.reduce<SentosaDailyReward[][]>(
          (acc, reward, index) => {
            const weekIndex = Math.floor(index / 7);
            if (!acc[weekIndex]) acc[weekIndex] = [];
            acc[weekIndex].push(reward);
            return acc;
          },
          []
        );
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchCapitalandGeneoCoin() {
      try {
        const { data } = await CAPITALAND.geneoCoins();

        this.capitalandGeneoCoin = data.map((c, index) => ({
          ...c,
          geojson_file: `lydenwoods_${index + 1}`,
        }));
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchCapitalandAmenities() {
      try {
        const { data } = await CAPITALAND.getAmenities();

        this.capitalandAmenities = data;
      } catch (error) {
        // console.error('error', error);
      }
    },

    async fetchInventory() {
      try {
        const { data } = await INVENTORY.get();
        this.inventoryData = {
          items: data.items,
          inventory_info: data.inventory_info,
        };
      } catch (error) {
        // console.error('error', error);
      }
    },
  },
});
