import { defineStore } from 'pinia';
import type { IInstructor, InventoryItem, IShrinkData } from '@types';

interface DialogState {
  welcomeSafetyDate: string;
  welcomeSafetyTicked: boolean;
  welcomeGoldenDroppedTicked: boolean;
  hoursBonusTicked: boolean;
  dailyLoginTicked: boolean;
  showHuntingStopTrial: boolean;
  contestAnnouncementTicked: boolean;
  redirectedFromTicked: boolean;
  gpsTapped: boolean;
  showInstructor: 'timii' | 'nancii' | 'sqkii' | 'shinobii' | 'rmi' | '';
  instructorData: IInstructor;
  shrinkData: IShrinkData | null;
  isBack: boolean;
  enterSerialNumberTicked: boolean;
  otpAnnouncementTicked: boolean;
  subDomainTicket: boolean;
  showCoinSonarGUI: boolean;
  showBeaconGUI: boolean;
  showMetalDetectorGUI: boolean;
  showSilverCoinSelectCircle: boolean;
  metalSonarPowerUpTicked: boolean;
  triggerGoldenSequence: boolean;
  toggle: boolean;
  spfStartTimeAt: number;
  triggerGuestReminder:
    | 'claimed_ba'
    | 'use_shrink'
    | 'grids_eliminated'
    | 'text_hints'
    | 'enter_serial_number'
    | '';
  toggleHunterTool: boolean;
  showedSentosaDailyReward: boolean;
  showBottomSheet: boolean;
  showInventoryQuickAccess: boolean;
  highLightedHunt: boolean;
  hiddenAmenity: boolean;
  triggerBagOnboarding: boolean;
  inventoryItem: InventoryItem | undefined;
  showRMI: boolean;
}

export const useDialogStore = defineStore('dialogs', {
  state: (): DialogState => ({
    welcomeSafetyDate: LocalStorage.getItem('welcome_safety') || '',
    welcomeSafetyTicked: false,
    welcomeGoldenDroppedTicked: false,
    hoursBonusTicked: false,
    dailyLoginTicked: false,
    showHuntingStopTrial: false,
    contestAnnouncementTicked: false,
    redirectedFromTicked: false,
    gpsTapped: false,
    showInventoryQuickAccess: false,
    showInstructor: '',
    instructorData: {
      agent: '',
      tag: '',
      sequences: [],
      hiddenAnims: false,
      bubbleAction: true,
    },
    // totalTimeShrank: 0,
    shrinkData: null,
    isBack: false,
    enterSerialNumberTicked: false,
    otpAnnouncementTicked: false,
    subDomainTicket: false,
    showCoinSonarGUI: false,
    showBeaconGUI: false,
    showMetalDetectorGUI: false,
    showSilverCoinSelectCircle: false,
    metalSonarPowerUpTicked: false,
    triggerGoldenSequence: false,
    toggle: false,
    spfStartTimeAt: 0,
    triggerGuestReminder: '',
    toggleHunterTool: false,
    showedSentosaDailyReward: false,
    showBottomSheet: false,
    highLightedHunt: false,
    hiddenAmenity: true,
    triggerBagOnboarding: false,
    inventoryItem: undefined,
    showRMI: false,
  }),

  getters: {
    //
  },

  actions: {
    setWelcomeSafetyDate(date: string) {
      this.welcomeSafetyTicked = true;
      if (!date) LocalStorage.remove('welcome_safety');
      else LocalStorage.set('welcome_safety', date);
      this.welcomeSafetyDate = date;
    },

    openUnifyInstructor(
      type: 'sqkii' | 'timii' | 'nancii' | 'shinobii' | 'rmi',
      params: IInstructor
    ) {
      if (!!this.showInstructor) this.closeUnifyInstructor();

      this.showInstructor = type;
      this.instructorData.bubbleText = params.bubbleText;
      this.instructorData.agent = params.agent;
      this.instructorData.tag = params.tag;
      this.instructorData.sequences = params.sequences;
      this.instructorData.hiddenAnims = params.hiddenAnims;
      this.instructorData.bubbleAction = params.bubbleAction;
    },

    closeUnifyInstructor() {
      this.showInstructor = '';
      this.shrinkData = null;
      this.instructorData.agent = '';
      this.instructorData.tag = '';
      this.instructorData.bubbleText = '';
      this.instructorData.sequences = [];
      this.instructorData.hiddenAnims = false;
      this.instructorData.bubbleAction = true;
    },
  },
});
