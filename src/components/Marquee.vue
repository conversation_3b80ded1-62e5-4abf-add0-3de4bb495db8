<template>
  <div class="marquee">
    <div class="track">
      <span
        class="text-sm"
        v-html="DEFAULT_SAFETY.map((item) => item + '&ensp;').join('&nbsp;')"
      ></span>
    </div>
  </div>
</template>

<script setup lang="ts">
const { t } = useI18n();

const DEFAULT_SAFETY = computed(() => [
  t('SAFETY_HINT1_TITLE'),
  t('SAFETY_HINT2_TITLE'),
  t('SAFETY_HINT3_TITLE'),
  t('SAFETY_HINT4_TITLE'),
  t('SAFETY_HINT5_TITLE'),
  t('SAFETY_HINT6_TITLE'),
  t('SAFETY_HINT7_TITLE'),
  t('SAFETY_HINT8_TITLE'),
]);

const duration = computed(() => {
  const time = DEFAULT_SAFETY.value.length * 3 + 5;
  return `${time}s`;
});
</script>

<style scoped lang="scss">
.marquee {
  position: fixed;
  z-index: 2;
  bottom: 0;
  height: 30px;
  width: 100vw;
  max-width: 100%;
  overflow-x: hidden;
  background: rgba(#22214f, 0.8) !important;
  padding: 4px;
  .track {
    position: absolute;
    white-space: nowrap;
    will-change: transform;
    animation: marquee v-bind(duration) linear infinite;
  }

  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(-50%);
    }
  }
}
</style>
