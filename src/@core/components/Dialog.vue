<script setup lang="ts">
interface Props {
  hideClose?: boolean;
  noHeader?: boolean;
  crystals?: boolean;
  backdrop?: boolean;
  backdropHeader?: boolean;
  bottomCrystal?: boolean;
  px?: number;
  bgHeader?: string;
}

interface Emits {
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emits = defineEmits<Emits>();

const pDialog = props.px
  ? `16px ${props.px}px 28px ${props.px}px`
  : '16px 20px 28px 20px';
</script>

<template>
  <div class="fullscreen">
    <div v-if="backdrop" class="backdrop-css"></div>
    <div class="absolute top-4 right-4" v-if="crystals">
      <HeaderCrystal />
    </div>
    <div id="general-dialog" class="general-dialog">
      <div class="full-width general-dialog-container column justify-center">
        <slot name="icon-center" />

        <slot name="bottom-frame" />

        <Icon
          v-if="bottomCrystal"
          class="bottom-crystal"
          name="bottom-crystal-dialog"
        />

        <div class="absolute -top-2 -right-2 z-10">
          <slot name="btnTopRight"></slot>
        </div>

        <Button
          v-if="!hideClose && !backdropHeader"
          class="absolute -top-2 -right-2 z-10"
          flat
          @click="emits('close')"
          shape="square"
          size="small"
        >
          <Icon name="cross" :size="16" />
        </Button>

        <div class="absolute -top-2 -left-2 z-10">
          <slot name="btnTopLeft"></slot>
        </div>
        <div class="dialog-animated__header" v-if="!noHeader">
          <div v-if="backdropHeader" class="backdrop-header"></div>
          <div
            class="absolute-full z-10"
            style="z-index: 1"
            :class="bgHeader ? bgHeader : 'bg'"
          ></div>
          <div class="relative absolute-full flex flex-center z-20">
            <div class="text-lg font-bold">
              <slot name="header" />
            </div>
          </div>
        </div>
        <div class="full-width">
          <slot name="special-box"></slot>
        </div>
        <div class="absolute top-1/4 left-0 z-50">
          <slot name="sov" />
        </div>
        <div class="full-width flex-1 dialog-content">
          <slot />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.general-dialog {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  overflow-y: hidden;
  flex-direction: column;

  &-container {
    background-image: url('/imgs/dialog_top.png'),
      url('/imgs/dialog_center.png'), url('/imgs/dialog_bottom.png');
    max-height: 85%;
    background-position: center 1px, center 22.5px, center bottom;
    background-size: 100% 22px, 100% calc(100% - 62px), 100% 40px;
    background-repeat: no-repeat;
    box-shadow: none;
    height: max-content;
    min-height: 200px;
    position: relative;
  }

  .dialog-animated {
    position: relative;
    width: 100%;
    color: #ffffff;

    &__header {
      position: relative;
      width: 100%;
      margin-top: 8vw;
      padding: 15px;
      text-align: center;
      font-size: 16px;
      line-height: 16px;
      font-weight: bold;

      .bg {
        background: linear-gradient(
          270deg,
          rgba(135, 100, 242, 0) -37.14%,
          #8764f2 46.61%,
          rgba(141, 118, 211, 0) 125.87%
        );
        opacity: 0.3;
      }
      .bg-island-bounty {
        background: linear-gradient(
          270deg,
          rgba(135, 100, 242, 0) -37.14%,
          rgba(245, 146, 0, 0.69) 46.61%,
          rgba(141, 118, 211, 0) 125.87%
        );
      }
    }

    &__container {
      width: 100%;
      padding: 20px 20px;
      position: relative;
      z-index: 7000;
      overflow: auto;
      max-height: calc(100vh - 140px);
    }
    &__footer {
      width: 100%;
      padding: 10px 20px;
    }
  }

  .dialog-content {
    overflow-y: auto;
    overflow-x: hidden;
    padding: v-bind(pDialog);
    clip-path: polygon(
      100% 0,
      100% calc(100% - 15px),
      0 calc(100% - 15px),
      0 0
    );
  }
}
.backdrop-header {
  position: absolute;
  top: -8vw;
  left: 0;
  width: 100%;
  height: calc(100% + 8vw);
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 99;
}

.backdrop-css {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 8888;
  background-color: rgba(0, 0, 0, 0.5);
}

.bottom-crystal {
  position: absolute;
  width: calc(100% + 30px);
  bottom: -8px;
  left: -15px;
  z-index: 10;
  pointer-events: none;
}
</style>
