import type { THintRarity } from '@types';

export enum SOCKET_EVENT {
  UPDATE_MAP = 'update-map',
  UPDATE_CIRCLE = 'update_circle',
  UPDATE_BUILD = 'update_build',
  UPDATE_SETTING = 'update_setting',
  ANNOUNCEMENT = 'announcement',
  GRID_ELIMINATED = 'grid_eliminated',
  UPDATE_GOLDEN_COIN = 'update-golden-coin',
  LUCKY_TEXT_HINT = 'lucky_text_hint',
  LUCKY_ELIMINATE_POWER_UP = 'lucky_eliminate_power_up',
  ZALO_DAILY_QUOTA = 'zalo_daily_quota',
  BRAND_ACTION_UPDATE = 'brand_action',
  BRAND_ACTION_UPDATED = 'brand_action_updated',
  CONTENT_UPDATE = 'content_updated',
  BEACON_SPAWN = 'beacon_spawn',
  CRYSTAL_EXPIRED = 'crystal_expired',
  CRYSTAL_UPDATE = 'crystal_update',
  CAPITALAND_GENEO_COIN_1 = 'capita_land_golden_coin',
  SENTOSA_GOLDEN_HINT = 'sentosa_golden_hint',
  SENTOSA_DAILY_REWARD = 'sentosa_daily_reward',
  BEACH_STATION_COUNTER = 'beach_station_counter',
  SENTOSA_ISLAND_BOUNTY_TOTAL_TIME = 'sentosa_island_bounty_total_time',
  CAPITALAND_GENEO_COIN = 'capita-land-golden-coin',
  CAPITALAND_GENEO_HINT = 'capita-land-golden-hint',
  CAPITALAND_GENEO_DAILY_REWARD = 'capita_land_daily_reward',
}

// keyCode constants
export const BACKSPACE = 8;
export const LEFT_ARROW = 37;
export const RIGHT_ARROW = 39;
export const DELETE = 46;

export const REGEX_DIGIT = /\d/;
export const REGEX_LOWERCASE = /[a-z]/;
export const REGEX_UPPERCASE = /[A-Z]/;
export const REGEX_SPECIAL = /[`~!@#$%^&*()\-_+=[{\]}\\|;:'",<.>/?]/;

export enum BRAND_SOV {
  SMRT = 'smrt',
  ETIQA = 'etiqa',
  SQKII = 'sqkii',
  TIGER_BROKER = 'tiger_broker',
  SINGAPORE_POLICE_FORCE = 'singapore_police_force',
  MYVILLAGE = 'myvillage',
  NESTLE_PROFESSIONAL = 'nestle_professional',
  LENDLEASE = 'lendlease',
  BURGER_KING = 'burger_king',
  SENTOSA = 'sentosa',
  CAPITALAND = 'capitaland',
  CAPITALAND_SCIENCE_PARK = 'capitaland_science_park',
  CAPITALAND_GENEO = 'capitaland_geneo',
  DBS = 'dbs',
}

export const BRAND_SOV_LABELS = {
  [BRAND_SOV.SMRT]: 'SMRT',
  [BRAND_SOV.ETIQA]: 'Etiqa',
  [BRAND_SOV.SQKII]: 'Sqkii',
  [BRAND_SOV.TIGER_BROKER]: 'Tiger Broker',
  [BRAND_SOV.SINGAPORE_POLICE_FORCE]: 'Singapore Police Force',
  [BRAND_SOV.MYVILLAGE]: 'myVillage',
  [BRAND_SOV.NESTLE_PROFESSIONAL]: 'Nestle Professional',
  [BRAND_SOV.LENDLEASE]: 'Lendlease',
  [BRAND_SOV.BURGER_KING]: 'Burger King',
  [BRAND_SOV.SENTOSA]: 'Sentosa',
  [BRAND_SOV.CAPITALAND]: 'CapitaLand',
  [BRAND_SOV.CAPITALAND_SCIENCE_PARK]: 'Science Park',
  [BRAND_SOV.CAPITALAND_GENEO]: 'LyndenWoods',
  [BRAND_SOV.DBS]: 'DBS',
};

export const INDEX_RARITY: Record<THintRarity, number> = {
  C: 1,
  U: 2,
  R: 3,
  SR: 4,
  SSR: 5,
};

export enum SqkiiVouchersTransactionType {
  TOP_UP = 'topup',
  PAYMENT = 'payment',
}

export const SqkiiVouchersTransactionTypeLabels = {
  [SqkiiVouchersTransactionType.TOP_UP]: 'Get Vouchers',
  [SqkiiVouchersTransactionType.PAYMENT]: 'Use Vouchers',
};

export enum SqkiiVouchersTransactionStatus {
  COMPLETED = 'completed',
  FAILED = 'failed',
  REFUNDED = 'refunded',
}

export enum InventoryItemType {
  SHRINK = 'shrink',
  SHRINK_LITE = 'shrink_lite',
  COIN_SONAR = 'coin_sonar',
  METAL_DETECTOR = 'metal_detector',
  BEACON = 'beacon',
}

export enum InventoryItemCategory {
  VOUCHER = 'voucher',
  POWER_UP = 'power_up',
  FOOD = 'food',
}

export enum HuntingStopRewardType {
  CRYSTAL = 'crystal',
  SHRINK_LITE = 'shrink_lite',
  SHRINK = 'shrink',
  BEACON = 'beacon',
  COIN_SONAR = 'coin_sonar',
  METAL_DETECTOR = 'metal_detector',
}

export enum HuntingStopType {
  NORMAL = 'normal',
  MEGA = 'mega',
}

export const HUNTINGSTOPREWARDINFO: Record<
  HuntingStopRewardType,
  { name: string; icon: HuntingStopRewardType; des: string }
> = {
  [HuntingStopRewardType.CRYSTAL]: {
    name: 'Crystal',
    icon: HuntingStopRewardType.CRYSTAL,
    des: 'REWARD_DES_CRYSTAL',
  },
  [HuntingStopRewardType.SHRINK_LITE]: {
    name: 'Shrink Lite',
    icon: HuntingStopRewardType.SHRINK_LITE,
    des: 'REWARD_DES_SHRINK_LITE',
  },
  [HuntingStopRewardType.SHRINK]: {
    name: 'Shrink',
    icon: HuntingStopRewardType.SHRINK,
    des: 'REWARD_DES_SHRINK',
  },
  [HuntingStopRewardType.BEACON]: {
    name: 'Beacon',
    icon: HuntingStopRewardType.BEACON,
    des: 'REWARD_DES_BEACON',
  },
  [HuntingStopRewardType.COIN_SONAR]: {
    name: 'Coin Sonar',
    icon: HuntingStopRewardType.COIN_SONAR,
    des: 'REWARD_DES_COIN_SONAR',
  },
  [HuntingStopRewardType.METAL_DETECTOR]: {
    name: 'Metal Detector',
    icon: HuntingStopRewardType.METAL_DETECTOR,
    des: 'REWARD_DES_METAL_DETECTOR',
  },
};
