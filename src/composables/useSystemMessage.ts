import { last, shuffle } from 'lodash';
import { useGlobalInstructor, useSilverCoinSov } from '@composables';
import { useDialogStore, useMapStore } from '@stores';
import { RmiOutlet, RmiOutletType, SystemMessage } from '@types';

interface MessageState {
  currentMessageId: string;
  currentTimeout: NodeJS.Timeout | null;
  nextMessageTimeout: NodeJS.Timeout | null;
  sequenceMessageQueue: string[];
  triggeredSequenceMessages: Set<string>;
  triggeredConditionalMessages: Set<string>;
  triggeredRmiConditionalMessages: Set<string>;
  lastTriggeredSequenceMessageId: string;
  isProcessing: boolean;
  attemptsTriggered: number;
  rmiCooldowns: Map<string, number>;
}

type MessageType = 'timii' | 'sqkii' | 'nancii' | 'rmi';
type MessageCategory = 'sequence' | 'conditional' | 'rmi_conditional';

const MESSAGE_TYPES: Record<string, MessageType> = {
  TIMII: 'timii',
  SQKII: 'sqkii',
  NANCII: 'nancii',
  RMI: 'rmi',
} as const;

const MESSAGE_CATEGORIES: Record<string, MessageCategory> = {
  SEQUENCE: 'sequence',
  CONDITIONAL: 'conditional',
  RMI_CONDITIONAL: 'rmi_conditional',
} as const;

const TIMING_CONSTANTS = {
  MESSAGE_CHECK_DELAY: 300, // 300ms between checks
  AUTO_CLOSE_TIMEOUT: 15000, // 15 seconds auto-close
  UI_SETTLE_DELAY: 50, // 50ms for UI to settle
  RMI_COOLDOWN_PERIOD: 60 * 1000, // 1 minute in milliseconds
  MAX_SHUFFLE_ATTEMPTS: 15,
  MAX_UI_WAIT_ATTEMPTS: 10,
} as const;

const SHRINK_MESSAGE_KEYS = {
  smaller_public: 'SHRINKINGCIRCLE_SHRANKED_AHEAD',
  smaller_private: 'SHRINKINGCIRCLE_SHRANKED_AHEAD_1',
  default: 'SHRINKINGCIRCLE_SHRANKED_AHEAD_2',
} as const;

const RMI_OUTLET_TYPES = ['poi', 'landmark', 'merchant_rmi'] as RmiOutletType[];

const createMessage = (
  id: string,
  text: string,
  type: MessageType,
  messageType: MessageCategory,
  options: Partial<SystemMessage> = {}
): SystemMessage => ({
  id,
  text,
  type,
  messageType,
  ...options,
});

export function useSystemMessage() {
  const storeDialog = useDialogStore();
  const storeMap = useMapStore();

  const { _loading, rmiOutlets } = storeToRefs(storeMap);
  const { showInstructor, shrinkData } = storeToRefs(storeDialog);
  const { openUnifyInstructor, closeUnifyInstructor } = useGlobalInstructor();
  const { dialogs, currentPath } = useMicroRoute();
  const { shrinkSovContext } = useSilverCoinSov();
  const { t } = useI18n();

  const state = reactive<MessageState>({
    currentMessageId: '',
    currentTimeout: null,
    nextMessageTimeout: null,
    sequenceMessageQueue: [],
    triggeredSequenceMessages: new Set(),
    triggeredConditionalMessages: new Set(),
    triggeredRmiConditionalMessages: new Set(),
    lastTriggeredSequenceMessageId: '',
    isProcessing: false,
    attemptsTriggered: 0,
    rmiCooldowns: new Map(),
  });

  const activePowerUp = computed(() =>
    [
      storeDialog.showCoinSonarGUI,
      storeDialog.showBeaconGUI,
      storeDialog.showMetalDetectorGUI,
      storeDialog.showInventoryQuickAccess,
      storeDialog.showSilverCoinSelectCircle,
      storeDialog.showHuntingStopTrial,
    ].some(Boolean)
  );

  const activeDialog = computed(() => dialogs.value.some((d) => d.actived));
  const activePage = computed(() => last(currentPath.value.split('/')));

  const isIdleInHomeScreen = computed(() => {
    if (_loading.value || activePowerUp.value) return false;
    const isHomeScreen = activePage.value === 'home';
    const noDialog = !activeDialog.value;
    return [isHomeScreen, noDialog].every(Boolean);
  });

  // ===== SYSTEM MESSAGES =====
  const systemSequenceMessages = computed<SystemMessage[]>(() => [
    createMessage(
      'sequence_tip_1',
      'Test new message system 1',
      MESSAGE_TYPES.TIMII,
      MESSAGE_CATEGORIES.SEQUENCE
    ),
    createMessage(
      'sequence_tip_2',
      'Test new message system 2 (Persistent - no auto-close)',
      MESSAGE_TYPES.TIMII,
      MESSAGE_CATEGORIES.SEQUENCE,
      { persistent: true }
    ),
    createMessage(
      'sequence_tip_3',
      'Test new message system 3',
      MESSAGE_TYPES.TIMII,
      MESSAGE_CATEGORIES.SEQUENCE
    ),
  ]);

  // ===== EXTERNAL MESSAGES =====
  const silverShrinkText = computed(() => {
    if (!shrinkData.value?.bottomMessage) return '';

    const { bottomMessage } = shrinkData.value;
    const { type, this_use, smallest } = bottomMessage;

    const translationKey =
      SHRINK_MESSAGE_KEYS[type as keyof typeof SHRINK_MESSAGE_KEYS] ||
      SHRINK_MESSAGE_KEYS.default;

    return t(translationKey, {
      USED: this_use,
      SMALLEST: smallest,
    });
  });

  const systemExternalMessages = computed<SystemMessage[]>(() => [
    createMessage(
      'silver_coin_shrink',
      silverShrinkText.value,
      MESSAGE_TYPES.NANCII,
      MESSAGE_CATEGORIES.CONDITIONAL,
      {
        backdropCss: true,
        agent: shrinkSovContext.value?.agent,
        tag: shrinkSovContext.value?.tag,
        condition: () => false,
      }
    ),
  ]);

  // ===== RMI CONDITIONAL MESSAGES =====
  const createRmiConditionalMessage = (outlet: RmiOutlet): SystemMessage => {
    const messageId = outlet.unique_id;

    const canTrigger = (): boolean => {
      const now = Date.now();

      const lastTriggered = state.rmiCooldowns.get(outlet.unique_id);
      if (
        lastTriggered &&
        now - lastTriggered < TIMING_CONSTANTS.RMI_COOLDOWN_PERIOD
      )
        return false; // Still in cooldown
      return true;
    };

    const text =
      outlet.fun_facts[Math.floor(Math.random() * outlet.fun_facts.length)];
    const images = outlet.type === 'merchant_rmi' ? outlet.images : [];

    return createMessage(
      messageId,
      t(text),
      MESSAGE_TYPES.RMI,
      MESSAGE_CATEGORIES.RMI_CONDITIONAL,
      {
        images,
        rmiOutlet: outlet,
        condition: canTrigger,
        onTrigger: () => {
          state.rmiCooldowns.set(outlet.unique_id, Date.now());
        },
      }
    );
  };

  const filteredRMIOutlets = computed(() =>
    rmiOutlets.value.filter((outlet) => RMI_OUTLET_TYPES.includes(outlet.type))
  );

  const systemRmiConditionalMessages = computed<SystemMessage[]>(() =>
    filteredRMIOutlets.value.map(createRmiConditionalMessage)
  );

  // ===== CONDITIONAL MESSAGES =====
  const systemConditionalMessages = computed<SystemMessage[]>(() => []);

  // ===== MESSAGE COLLECTIONS =====
  const systemMessages = computed<SystemMessage[]>(() => [
    ...systemSequenceMessages.value,
    ...systemExternalMessages.value,
    ...systemConditionalMessages.value,
    ...systemRmiConditionalMessages.value,
  ]);

  const sequenceMessages = computed<SystemMessage[]>(() =>
    systemMessages.value.filter(
      (msg) => msg.messageType === MESSAGE_CATEGORIES.SEQUENCE
    )
  );

  const conditionalMessages = computed<SystemMessage[]>(() =>
    systemMessages.value.filter(
      (msg) => msg.messageType === MESSAGE_CATEGORIES.CONDITIONAL
    )
  );

  const rmiConditionalMessages = computed<SystemMessage[]>(() =>
    systemMessages.value.filter(
      (msg) => msg.messageType === MESSAGE_CATEGORIES.RMI_CONDITIONAL
    )
  );

  // ===== SEQUENCE QUEUE MANAGEMENT =====
  const sequenceQueueManager = {
    initialize(avoidMessageId?: string): void {
      const messageIds = sequenceMessages.value.map((msg) => msg.id);

      if (messageIds.length <= 1) {
        state.sequenceMessageQueue = messageIds;
        state.triggeredSequenceMessages.clear();
        return;
      }

      // Shuffle until we get a different first message than the one to avoid
      let shuffledIds: string[];
      let attempts = 0;

      do {
        shuffledIds = shuffle(messageIds);
        attempts++;
      } while (
        avoidMessageId &&
        shuffledIds[0] === avoidMessageId &&
        attempts < TIMING_CONSTANTS.MAX_SHUFFLE_ATTEMPTS &&
        messageIds.length > 1
      );

      state.sequenceMessageQueue = shuffledIds;
      state.triggeredSequenceMessages.clear();
    },

    resetIfNeeded(): void {
      if (
        state.triggeredSequenceMessages.size >= sequenceMessages.value.length
      ) {
        const lastMessageId = state.lastTriggeredSequenceMessageId;
        state.triggeredSequenceMessages.clear();
        this.initialize(lastMessageId);
      }
    },

    getNext(): SystemMessage | null {
      this.resetIfNeeded();

      if (state.sequenceMessageQueue.length === 0) {
        return null;
      }

      // Find the next untriggered message in the queue
      for (const messageId of state.sequenceMessageQueue) {
        const message = systemMessages.value.find(
          (msg) => msg.id === messageId
        );
        if (message && !state.triggeredSequenceMessages.has(messageId)) {
          return message;
        }
      }

      return null;
    },
  };

  // ===== MESSAGE CHECKERS =====
  const messageCheckers = {
    checkRmiConditional(): SystemMessage | null {
      for (const message of rmiConditionalMessages.value) {
        if (message.condition && message.condition()) {
          if (!state.triggeredRmiConditionalMessages.has(message.id)) {
            return message;
          }
        }
      }
      return null;
    },

    checkConditional(): SystemMessage | null {
      for (const message of conditionalMessages.value) {
        if (message.condition && message.condition()) {
          if (!state.triggeredConditionalMessages.has(message.id)) {
            return message;
          }
        }
      }
      return null;
    },

    allSequenceTriggered(): boolean {
      return (
        state.triggeredSequenceMessages.size >= sequenceMessages.value.length
      );
    },

    allRmiConditionalTriggered(): boolean {
      const availableRmiMessages = rmiConditionalMessages.value.filter(
        (msg) => msg.condition && msg.condition()
      );
      return availableRmiMessages.length === 0;
    },

    allConditionalTriggered(): boolean {
      const availableConditionalMessages = conditionalMessages.value.filter(
        (msg) => msg.condition && msg.condition()
      );
      return (
        availableConditionalMessages.length === 0 ||
        availableConditionalMessages.every((msg) =>
          state.triggeredConditionalMessages.has(msg.id)
        )
      );
    },
  };

  // ===== RMI COOLDOWN MANAGER =====
  const rmiCooldownManager = {
    handlePlayerLeaving(): void {
      // When player leaves, clear any active RMI cooldowns that have expired
      // This allows re-triggering when they return after the cooldown period
      const now = Date.now();
      const expiredCooldowns: string[] = [];

      state.rmiCooldowns.forEach((timestamp, outletId) => {
        if (now - timestamp >= TIMING_CONSTANTS.RMI_COOLDOWN_PERIOD) {
          expiredCooldowns.push(outletId);
        }
      });

      // Remove expired cooldowns
      expiredCooldowns.forEach((outletId) => {
        state.rmiCooldowns.delete(outletId);
      });
    },
  };

  // ===== COMPLETION CHECKER =====
  const completionChecker = {
    allTypesCompleted(): boolean {
      const allRmiTriggered = messageCheckers.allRmiConditionalTriggered();
      const allConditionalTriggered = messageCheckers.allConditionalTriggered();
      const allSequenceTriggered = messageCheckers.allSequenceTriggered();

      // Only consider complete when we have messages to trigger
      const hasRmiMessages = rmiConditionalMessages.value.some(
        (msg) => msg.condition && msg.condition()
      );
      const hasConditionalMessages = conditionalMessages.value.some(
        (msg) => msg.condition && msg.condition()
      );
      const hasSequenceMessages = sequenceMessages.value.length > 0;

      // If no messages exist for a type, consider it "completed"
      const rmiComplete = !hasRmiMessages || allRmiTriggered;
      const conditionalComplete =
        !hasConditionalMessages || allConditionalTriggered;
      const sequenceComplete = !hasSequenceMessages || allSequenceTriggered;

      const isComplete = rmiComplete && conditionalComplete && sequenceComplete;

      return isComplete;
    },

    resetAllStates(): void {
      state.attemptsTriggered = 0;
      state.triggeredConditionalMessages.clear();
      state.triggeredRmiConditionalMessages.clear();
      state.triggeredSequenceMessages.clear();
      const lastMessageId = state.lastTriggeredSequenceMessageId;
      sequenceQueueManager.initialize(lastMessageId);
    },
  };

  // Trigger a message
  function triggerMessage(message: SystemMessage, isFromSystem = true): void {
    if (state.isProcessing) return;

    state.isProcessing = true;
    state.currentMessageId = message.id;

    uiUtils.clearAllTimeouts();
    // Call onTrigger if exists
    message.onTrigger?.();

    // Open instructor with message
    openUnifyInstructor(message.type, {
      agent: message.agent,
      tag: message.tag,
      hiddenAnims: message.hiddenAnims,
      sequences: [
        {
          backdropCss: message.backdropCss,
          message: message.text,
          images: message.images || [],
          rmiOutlet: message.rmiOutlet,
          actions: {
            cb(_, close) {
              close();
              message.onClick?.();
              handleMessageComplete(message);
            },
            closeX(_, close) {
              close();
              handleMessageComplete(message);
            },
          },
        },
      ],
    });

    // Auto-close logic:
    // 1. If message has persistent = true, never auto-close
    // 2. If message is from external source (not system), never auto-close
    // 3. Only auto-close messages from system that are not persistent
    const shouldAutoClose = isFromSystem && !message.persistent;

    if (shouldAutoClose) {
      state.currentTimeout = setTimeout(() => {
        closeUnifyInstructor();
        handleMessageComplete(message);
      }, TIMING_CONSTANTS.AUTO_CLOSE_TIMEOUT);
    }
  }

  // Handle message completion
  function handleMessageComplete(message: SystemMessage): void {
    // Prevent double handling
    if (!state.isProcessing) return;

    switch (message.messageType) {
      case MESSAGE_CATEGORIES.SEQUENCE:
        state.triggeredSequenceMessages.add(message.id);
        state.lastTriggeredSequenceMessageId = message.id;
        break;
      case MESSAGE_CATEGORIES.CONDITIONAL:
        state.triggeredConditionalMessages.add(message.id);
        break;
      case MESSAGE_CATEGORIES.RMI_CONDITIONAL:
        break;
      default:
        break;
    }

    state.isProcessing = false;
    state.currentMessageId = '';
    uiUtils.clearAllTimeouts();

    // Wait for UI to fully update before scheduling next check
    uiUtils.waitForUpdate(() => {
      messageScheduler.scheduleNext();
    });
  }

  // ===== UI UTILITIES =====
  const uiUtils = {
    waitForUpdate(
      callback: () => void,
      maxAttempts = TIMING_CONSTANTS.MAX_UI_WAIT_ATTEMPTS
    ): void {
      let attempts = 0;

      const checkUIState = () => {
        attempts++;

        if (
          (!showInstructor.value && isIdleInHomeScreen.value) ||
          attempts >= maxAttempts
        ) {
          callback();
          return;
        }

        // Wait a bit more and check again
        setTimeout(checkUIState, TIMING_CONSTANTS.UI_SETTLE_DELAY);
      };

      // Start checking after a small initial delay
      setTimeout(checkUIState, TIMING_CONSTANTS.UI_SETTLE_DELAY);
    },

    clearAllTimeouts(): void {
      [state.currentTimeout, state.nextMessageTimeout].forEach((timeout) => {
        if (timeout) {
          clearTimeout(timeout);
          timeout = null;
        }
      });
    },

    findMessageById(messageId: string): SystemMessage | undefined {
      return systemMessages.value.find((msg) => msg.id === messageId);
    },
  };

  // ===== MESSAGE SCHEDULER =====
  const messageScheduler = {
    scheduleNext(): void {
      if (state.nextMessageTimeout) clearTimeout(state.nextMessageTimeout);

      state.nextMessageTimeout = setTimeout(() => {
        this.checkAndTrigger();
      }, TIMING_CONSTANTS.MESSAGE_CHECK_DELAY);
    },

    checkAndTrigger(): void {
      // Extra check: ensure showInstructor is actually false
      if (showInstructor.value) {
        setTimeout(() => {
          this.checkAndTrigger();
        }, TIMING_CONSTANTS.UI_SETTLE_DELAY);
        return;
      }

      if (!isIdleInHomeScreen.value || state.isProcessing) {
        this.scheduleNext();
        return;
      }

      // Priority 1: Check RMI conditional messages first (only if not all triggered)
      if (!messageCheckers.allRmiConditionalTriggered()) {
        const rmiConditionalMessage = messageCheckers.checkRmiConditional();
        if (rmiConditionalMessage) {
          triggerMessage(rmiConditionalMessage);
          return;
        }
      }

      // Priority 2: Check conditional messages (only if all RMI completed and not all conditional triggered)
      if (
        messageCheckers.allRmiConditionalTriggered() &&
        !messageCheckers.allConditionalTriggered()
      ) {
        const conditionalMessage = messageCheckers.checkConditional();
        if (conditionalMessage) {
          triggerMessage(conditionalMessage);
          return;
        }
      }

      // Priority 3: Check sequence messages (only if all RMI and conditional completed)
      if (
        messageCheckers.allRmiConditionalTriggered() &&
        messageCheckers.allConditionalTriggered() &&
        !messageCheckers.allSequenceTriggered()
      ) {
        const sequenceMessage = sequenceQueueManager.getNext();
        if (sequenceMessage) {
          triggerMessage(sequenceMessage);
          return;
        }
      }

      // Check if all message types have been completed
      if (completionChecker.allTypesCompleted()) {
        completionChecker.resetAllStates();
        // Immediately schedule next check after reset
        this.scheduleNext();
        return;
      }

      // If no messages to trigger, schedule next check
      this.scheduleNext();
    },
  };

  function forceTriggermessage(messageId: string): void {
    const message = uiUtils.findMessageById(messageId);
    if (message) {
      triggerMessage(message);
    }
  }

  function stopTriggerMessage(): void {
    closeUnifyInstructor();
    uiUtils.clearAllTimeouts();
    state.isProcessing = false;
    state.currentMessageId = '';
  }

  function handleNextMessage(): void {
    const currentMessage = uiUtils.findMessageById(state.currentMessageId);
    if (currentMessage) {
      handleMessageComplete(currentMessage);
    }
  }

  watch(
    isIdleInHomeScreen,
    (isIdle) => {
      if (isIdle && !state.isProcessing && !showInstructor.value) {
        uiUtils.waitForUpdate(() => {
          messageScheduler.checkAndTrigger();
        });
      } else if (!isIdle) {
        // Handle player leaving - clean up expired RMI cooldowns
        rmiCooldownManager.handlePlayerLeaving();

        if (state.isProcessing && showInstructor.value) {
          // Only auto-close if a message is currently being shown
          closeUnifyInstructor();
          const currentMessage = uiUtils.findMessageById(
            state.currentMessageId
          );
          if (currentMessage) {
            handleMessageComplete(currentMessage);
          }
        }
      }
    },
    { immediate: true }
  );

  onMounted(async () => {
    await nextTick();
    sequenceQueueManager.initialize();
  });

  onUnmounted(() => {
    uiUtils.clearAllTimeouts();
    completionChecker.resetAllStates();
  });

  return {
    forceTriggermessage,
    stopTriggerMessage,
    closeUnifyInstructor,
    openUnifyInstructor,
    handleNextMessage,
  };
}
