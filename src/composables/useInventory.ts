import { InventoryItemType } from '@constants';
import { useDialogStore, useMapStore, useUserStore } from '@stores';
import { useGlobalInstructor, useTick } from '@composables';
import { InventoryItem } from '@types';
import { INVENTORY } from '@repositories';
import { errorNotify, successNotify } from '@helpers';

const TIME_UNITS = {
  MILLISECOND: 1,
  SECOND: 1000,
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
} as const;

export function useInventory() {
  const storeUser = useUserStore();
  const storeMap = useMapStore();
  const storeDialog = useDialogStore();

  const { openUnifyInstructor } = useGlobalInstructor();
  const { closeAllDialog, openDialog, push } = useMicroRoute();
  const { inventoryData, onboarding } = storeToRefs(storeUser);
  const { totalCoin } = storeToRefs(storeMap);

  const { now } = useTick();
  const { t } = useI18n();

  const loading = ref(false);

  const itemDisplayNames = {
    [InventoryItemType.SHRINK]: t('INVENTORY_SILVER_SHRINK'),
    [InventoryItemType.SHRINK_LITE]: t('INVENTORY_SILVER_SHRINK_LITE'),
    [InventoryItemType.COIN_SONAR]: t('INVENTORY_COIN_SONAR'),
    [InventoryItemType.METAL_DETECTOR]: t('INVENTORY_METAL_DETECTOR'),
    [InventoryItemType.BEACON]: t('INVENTORY_BEACON'),
  };

  const itemAssetNames = {
    [InventoryItemType.SHRINK]: 'silver-shrink',
    [InventoryItemType.SHRINK_LITE]: 'silver-shrink-lite',
    [InventoryItemType.COIN_SONAR]: 'metal-sonar',
    [InventoryItemType.METAL_DETECTOR]: 'metal-detector',
    [InventoryItemType.BEACON]: 'beacon',
  };

  const inventoryItems = computed(() => inventoryData.value.items);
  const inventoryInfo = computed(() => inventoryData.value.inventory_info);

  const hasInventory = computed(() => inventoryItems.value.length > 0);
  const totalItems = computed(() => {
    const getTotal = (items: InventoryItem[]) => {
      return items
        .map((item) => item.quantity)
        .reduce((acc, curr) => acc + curr, 0);
    };

    const totalShrink = getTotal(itemsQuickView.value.shrink);
    const totalShrinkLite = getTotal(itemsQuickView.value.shrinkLite);
    const totalCoinSonar = getTotal(itemsQuickView.value.coinSonar);
    const totalMetalDetector = getTotal(itemsQuickView.value.metalDetector);
    const totalBeacon = getTotal(itemsQuickView.value.beacon);

    return {
      totalShrink,
      totalShrinkLite,
      totalCoinSonar,
      totalMetalDetector,
      totalBeacon,
    };
  });

  const isFirstInventory = computed(() => !onboarding.value?.first_inventory);
  const isFirstInventoryQuickView = computed(
    () => !onboarding.value?.first_inventory_quick_view
  );
  const isFirstBeaconTrial = computed(
    () => !onboarding.value?.first_beacon_trial
  );
  const isFirstCoinSonarTrial = computed(
    () => !onboarding.value?.first_trial_coin_sonar
  );
  const isFirstMetalDetectorTrial = computed(
    () => !onboarding.value?.first_trial_metal_detector
  );
  const isFirstSilverShrinkTrial = computed(
    () => !onboarding.value?.first_trial_silver_shrink
  );

  const canTriggerInventoryOnboarding = computed(() => {
    return (
      hasInventory.value &&
      isFirstInventory.value &&
      isFirstInventoryQuickView.value
    );
  });

  const showInventoryIcon = computed(
    () =>
      canTriggerInventoryOnboarding.value || !isFirstInventoryQuickView.value
  );

  const TOTAL_ITEMS = computed(() =>
    inventoryItems.value.reduce((acc, curr) => acc + curr.quantity, 0)
  );
  const MAX_INVENTORY_SIZE = computed(
    () => inventoryInfo.value.max_inventory_size
  );
  const MAX_STACK_SIZE = computed(() => inventoryInfo.value.max_stack_size);
  const EXTENDED_SLOTS = computed(() => inventoryInfo.value.extended_slots);

  const itemsQuickView = computed(() => {
    const getItemsByType = (itemType: InventoryItemType) =>
      inventoryItems.value
        .filter((item) => item.item_type === itemType)
        .sort((a, b) => getTime(a.expires_at) - getTime(b.expires_at));

    return {
      shrink: getItemsByType(InventoryItemType.SHRINK),
      shrinkLite: getItemsByType(InventoryItemType.SHRINK_LITE),
      coinSonar: getItemsByType(InventoryItemType.COIN_SONAR),
      metalDetector: getItemsByType(InventoryItemType.METAL_DETECTOR),
      beacon: getItemsByType(InventoryItemType.BEACON),
    };
  });

  const getItemExpirationText = computed(
    () =>
      (item: InventoryItem): string => {
        const timeLeft = +new Date(item.expires_at) - now.value;
        return formatTimeLeft(timeLeft);
      }
  );

  const canUseSilverShrink = computed(
    () => itemsQuickView.value.shrink.length > 0 && totalCoin.value > 0
  );

  const canUseSilverShrinkLite = computed(
    () => itemsQuickView.value.shrinkLite.length > 0 && totalCoin.value > 0
  );

  const canUseCoinSonar = computed(
    () => itemsQuickView.value.coinSonar.length > 0
  );

  const canUseMetalDetector = computed(
    () => itemsQuickView.value.metalDetector.length > 0
  );

  const canUseBeacon = computed(() => itemsQuickView.value.beacon.length > 0);

  const formatTimeLeft = (timeLeft: number): string => {
    if (timeLeft <= 0) return 'EXPIRED';

    const days = Math.floor(timeLeft / TIME_UNITS.DAY);
    if (days > 0) return `${days} ${days > 1 ? 'DAYS' : 'DAY'} LEFT`;

    const hours = Math.floor(timeLeft / TIME_UNITS.HOUR);
    if (hours > 0) return `${hours} ${hours > 1 ? 'HOURS' : 'HOUR'} LEFT`;

    const minutes = Math.floor(timeLeft / TIME_UNITS.MINUTE);
    if (minutes > 0)
      return `${minutes} ${minutes > 1 ? 'MINUTES' : 'MINUTE'} LEFT`;

    const seconds = Math.floor(timeLeft / TIME_UNITS.SECOND);
    return `${seconds} ${seconds > 1 ? 'SECONDS' : 'SECOND'} LEFT`;
  };

  const hasExpiredItem = computed(() => {
    return inventoryItems.value.some(
      (item) => +new Date(item.expires_at) < now.value
    );
  });

  watch(hasExpiredItem, (value) => {
    if (value) storeUser.fetchInventory();
  });

  function handleViewPowerUpDetail(item: InventoryItem) {
    openDialog('inventory_detail', { inventoryItem: item });
  }

  function handleTrialSilverShrink() {
    push('/silver_coin_trial');
  }

  function handleTrialCoinSonar() {
    push('/coin_sonar_trial');
  }

  function handleTrialMetalDetector() {
    push('/metal_detector_trial');
  }

  function handleTrialBeacon() {
    push('/beacon_trial');
  }

  async function handleDiscardItem(item: InventoryItem, quantity: number) {
    try {
      loading.value = true;
      await INVENTORY.discard({ item_id: item._id, quantity });
      await storeUser.fetchInventory();
      closeAllDialog();
      successNotify({
        message: t('INVENTORY_DISCARD_SUCCESS', {
          QUANTITY: quantity,
          NAME: itemDisplayNames[item.item_type],
        }),
      });
    } catch (error) {
      console.error('error', error);
    } finally {
      loading.value = false;
    }
  }

  function remindTriggerGPS() {
    errorNotify({
      message: t('TRIAL_REMIND_GPS'),
    });
  }

  const getTime = (expires_at: string) => new Date(expires_at).getTime();

  function triggerFirstBagOnboarding() {
    storeDialog.triggerBagOnboarding = true;
    openUnifyInstructor('timii', {
      sequences: [
        {
          target: '.gui-bottom-right',
          css: {
            bottom: '10%',
          },
          hideClose: true,
          backdropCss: true,
          persistent: true,
          message: t('INVENTORY_FIRST_BAG_ONBOARDING_1'),
          actions: {
            cb: async (_, close) => {
              await close();
              storeDialog.showInventoryQuickAccess = true;
              triggerQuickViewOnboarding();
            },
          },
        },
      ],
    });
  }

  function triggerQuickViewOnboarding() {
    storeDialog.triggerBagOnboarding = false;
    openUnifyInstructor('timii', {
      sequences: [
        {
          target: '.gui',
          backdropCss: true,
          css: {
            bottom: '25vh',
          },
          hideClose: true,
          message: t('INVENTORY_FIRST_BAG_ONBOARDING_2'),
          actions: {
            cb: async (_, close) => {
              await close();
              push('inventory');
            },
          },
        },
      ],
    });
  }

  function triggerInventoryOnboarding() {
    openUnifyInstructor('timii', {
      sequences: [
        {
          message: t('INVENTORY_FIRST_BAG_ONBOARDING_3'),
        },
      ],
    });
  }

  return {
    loading,
    inventoryItems,
    hasInventory,
    itemsQuickView,
    MAX_INVENTORY_SIZE,
    MAX_STACK_SIZE,
    EXTENDED_SLOTS,
    TOTAL_ITEMS,
    canUseSilverShrink,
    canUseSilverShrinkLite,
    canUseCoinSonar,
    canUseMetalDetector,
    canUseBeacon,
    itemDisplayNames,
    itemAssetNames,
    getItemExpirationText,
    canTriggerInventoryOnboarding,
    showInventoryIcon,
    isFirstInventory,
    isFirstInventoryQuickView,
    isFirstBeaconTrial,
    isFirstCoinSonarTrial,
    isFirstMetalDetectorTrial,
    isFirstSilverShrinkTrial,
    totalItems,
    formatTimeLeft,
    getTime,
    handleViewPowerUpDetail,
    handleTrialSilverShrink,
    handleTrialCoinSonar,
    handleTrialMetalDetector,
    handleTrialBeacon,
    handleDiscardItem,
    triggerFirstBagOnboarding,
    triggerQuickViewOnboarding,
    triggerInventoryOnboarding,
    remindTriggerGPS,
  };
}
