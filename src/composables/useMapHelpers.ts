import ngeohash from 'ngeohash';
import transformTranslate from '@turf/transform-translate';
import bboxPolygon from '@turf/bbox-polygon';
import turfCircle from '@turf/circle';
import { useMapStore } from '@stores';
import { Feature, point, Polygon, Properties } from '@turf/helpers';
import {
  GeoJSONSourceSpecification,
  LngLatLike,
  SourceSpecification,
} from 'vue3-maplibre-gl';
import { LngLatBounds, LngLatBoundsLike } from 'maplibre-gl';
import intersect from '@turf/intersect';
import sgGeoJson from './json/sg_geojson.json';
import circle from '@turf/circle';
import bbox from '@turf/bbox';

interface FitBoundsOptions {
  lng: number;
  lat: number;
  radius: number;
  padding?: number;
  duration?: number;
  animate?: boolean;
}

export function useMapHelpers() {
  const storeMap = useMapStore();

  function encode(latitude: number, longitude: number) {
    const hash_string = ngeohash.encode(latitude, longitude, 6);
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const [_, minLong, __, maxLong] = ngeohash.decode_bbox(hash_string);
    const dLong = Math.abs(minLong - maxLong) / 2;
    const index = longitude < minLong + dLong ? 0 : 1;
    return `${hash_string}_${index}`;
  }

  function decode(hash: string) {
    const [hash_string, index] = hash.split('_');
    // eslint-disable-next-line prefer-const
    let [minLat, minLong, maxLat, maxLong] = ngeohash.decode_bbox(hash_string);
    const dLong = Math.abs(minLong - maxLong) / 2;
    if (+index) minLong += dLong;
    else maxLong -= dLong;
    const newDLat = Math.abs(minLat - maxLat) / 2;
    const newDLong = Math.abs(minLong - maxLong) / 2;
    return {
      latitude: minLat + newDLat,
      longitude: minLong + newDLong,
    };
  }

  function decodeBbox(hash: string) {
    const [hash_string, index] = hash.split('_');
    // eslint-disable-next-line prefer-const
    let [minLat, minLong, maxLat, maxLong] = ngeohash.decode_bbox(hash_string);
    const dLong = Math.abs(minLong - maxLong) / 2;
    if (+index) minLong += dLong;
    else maxLong -= dLong;
    return [minLong, minLat, maxLong, maxLat];
  }

  function mapTransform(
    lng: number,
    lat: number,
    radius: number,
    degrees: number
  ) {
    return transformTranslate(point([lng, lat]), radius, degrees, {
      units: 'meters',
    }).geometry.coordinates;
  }

  function transformPosition(geo: any, distance: number) {
    return transformTranslate(geo, distance + 10, 0, {
      units: 'meters',
    });
  }

  function polyMask(bbox: any, properties?: any) {
    const polygon = bboxPolygon(bbox, {
      properties,
    });

    return intersect(polygon as any, sgGeoJson.features[0] as any) as Feature<
      Polygon,
      Properties
    >;
  }

  function makeCircleFeature(
    lat: number,
    lng: number,
    radius: number,
    options: any = {}
  ) {
    return turfCircle([lng, lat], radius, {
      steps: 100,
      units: 'meters',
      ...options,
    });
  }

  function makeSource(features: any[]): GeoJSONSourceSpecification['data'] {
    return {
      type: 'FeatureCollection',
      features: Array.isArray(features) ? features : [],
    };
  }

  function makeSpecificationSource(features: any[]): SourceSpecification {
    return {
      type: 'geojson',
      data: {
        type: 'FeatureCollection',
        features: Array.isArray(features) ? features : [],
      },
    };
  }

  function circleToSource(circles?: any[]) {
    if (!circles?.length) return makeSource([]);
    return makeSource(
      circles.map((circle) =>
        makeCircleFeature(circle.lat, circle.lng, circle.radius)
      )
    );
  }

  function lngLatTransformPosition(
    lnglat: [number, number],
    radius: number
  ): LngLatLike {
    const geo = transformPosition(point(lnglat), radius);
    const [lng, lat] = geo.geometry.coordinates;
    return [lng, lat];
  }

  // function newPopUp(options: IPopUpOptions) {
  //   if (!storeMap.mapIns) return;

  //   const popup = new Popup({
  //     closeButton: options.closeButton,
  //     closeOnClick: options.closeOnClick,
  //     closeOnMove: options.closeOnMove,
  //     focusAfterOpen: options.focusAfterOpen,
  //     anchor: options.anchor,
  //     offset: options.offset,
  //     className: options.className,
  //     maxWidth: options.maxWidth,
  //   })
  //     .setLngLat(options.lngLat)
  //     .setHTML(options.html)
  //     .addTo(storeMap.mapIns as any);
  //   return popup;
  // }

  function togglePopupVisibility(target: string, show: boolean) {
    const popups = document.querySelectorAll(target);
    popups.forEach((p) => {
      p.classList.toggle('show', show);
      p.classList.toggle('hide', !show);
    });
  }

  function removePopup(target: string) {
    const popups = document.querySelectorAll(target);
    if (popups instanceof NodeList) popups.forEach((p) => p.remove());
  }

  function isWithinBounds(lng: number, lat: number, bounds: LngLatBounds) {
    return (
      lng >= bounds.getWest() &&
      lng <= bounds.getEast() &&
      lat >= bounds.getSouth() &&
      lat <= bounds.getNorth()
    );
  }

  async function fitBounds(options: FitBoundsOptions) {
    const {
      lng,
      lat,
      radius,
      padding = 50,
      duration = 500,
      animate = true,
    } = options;
    const _circle = circle([lng, lat], radius, {
      units: 'meters',
    });
    const box = bbox(_circle) as LngLatBoundsLike;

    storeMap.mapIns?.fitBounds(box, {
      padding,
      duration,
      animate,
    });
  }

  return {
    encode,
    decode,
    decodeBbox,
    mapTransform,
    transformPosition,
    polyMask,
    circleToSource,
    makeCircleFeature,
    makeSource,
    makeSpecificationSource,
    lngLatTransformPosition,
    // newPopUp,
    togglePopupVisibility,
    removePopup,
    isWithinBounds,
    fitBounds,
  };
}
