import { EventCallback, Rive, RiveParameters } from '@rive-app/canvas';

export interface RiveViewModel {
  path: string;
  type: 'enum' | 'boolean' | 'number' | 'trigger';
  value: string | boolean | number | EventCallback;
}

export interface UseRiveOptions extends Omit<RiveParameters, 'canvas'> {
  canvas: Ref<HTMLCanvasElement | null>;
  data?: RiveViewModel[];
}

export interface UseRiveReturn {
  bindViewModelInstance: (data: RiveViewModel[]) => void;
  isInitialized: Readonly<Ref<boolean>>;
}

interface ViewModelProperty {
  on?: (callback: EventCallback) => void;
  value?: string | boolean | number;
}

type RiveViewModelInstance = any & {
  enum: (path: string) => ViewModelProperty | null;
  boolean: (path: string) => ViewModelProperty | null;
  number: (path: string) => ViewModelProperty | null;
  trigger: (path: string) => ViewModelProperty | null;
};

export function useRive(options: UseRiveOptions): UseRiveReturn {
  let rive: Rive | null = null;
  const isInitialized = ref(false);

  const bindSingleProperty = (
    vmi: RiveViewModelInstance,
    item: RiveViewModel
  ): void => {
    try {
      const viewModelProperty = vmi[item.type]?.(item.path);

      if (!viewModelProperty) {
        console.warn(
          `ViewModel property not found: ${item.type}(${item.path})`
        );
        return;
      }

      if (typeof item.value === 'function') {
        viewModelProperty.on?.(item.value);
      } else if (item.type !== 'trigger' && 'value' in viewModelProperty) {
        viewModelProperty.value = item.value;
      }
    } catch (error) {
      console.error(`Error binding ${item.type}(${item.path}):`, error);
    }
  };

  const getViewModelInstance = (): RiveViewModelInstance | null => {
    if (!rive) {
      console.error('Rive instance not initialized');
      return null;
    }

    const vm = rive.defaultViewModel();
    if (!vm) {
      console.error('No default ViewModel found');
      return null;
    }

    const vmi = vm.defaultInstance();
    if (!vmi) {
      console.error('No default ViewModelInstance found');
      return null;
    }

    return vmi;
  };

  const bindViewModelData = (
    data: RiveViewModel[],
    shouldBind = false
  ): void => {
    if (!data.length) return;

    const vmi = shouldBind ? getViewModelInstance() : rive?.viewModelInstance;

    if (!vmi) {
      console.error('No ViewModelInstance available');
      return;
    }

    data.forEach((item) => bindSingleProperty(vmi, item));

    if (shouldBind && rive) {
      rive.bindViewModelInstance(vmi);
    }
  };

  const init = (): void => {
    if (!options.canvas.value) {
      console.error('Canvas element is not defined');
      return;
    }

    try {
      rive = new Rive({
        ...options,
        canvas: options.canvas.value,
        onLoad: (params) => {
          if (!rive) return;

          rive.resizeDrawingSurfaceToCanvas();
          options.onLoad?.(params);

          if (options.data) {
            bindViewModelData(options.data, true);
          }

          isInitialized.value = true;
        },
        onLoadError: (error) => {
          console.error('Failed to load Rive file:', error);
          options.onLoadError?.(error);
        },
      });
    } catch (error) {
      console.error('Failed to initialize Rive:', error);
    }
  };

  const bindViewModelInstance = (data: RiveViewModel[]): void => {
    bindViewModelData(data, false);
  };

  const cleanup = (): void => {
    if (rive) {
      rive.cleanup();
      rive = null;
      isInitialized.value = false;
    }
  };

  onMounted(async () => {
    await nextTick();
    init();
  });

  onBeforeUnmount(() => {
    cleanup();
  });

  return {
    bindViewModelInstance,
    isInitialized: readonly(isInitialized),
  };
}
